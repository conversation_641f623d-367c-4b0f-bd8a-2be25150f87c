"""
GPU内存优化器 - 专为双RTX 4090环境设计

优化策略：
1. 教师模型(Qwen3-8B): GPU 0, bfloat16 (16GB)
2. 学生模型(Qwen3-1.7B): GPU 1, bfloat16 (3.4GB)
3. 创新模块: GPU 1, 与学生模型共存 (2-3GB)
4. 总内存使用: GPU 0 (16GB/24GB), GPU 1 (6-7GB/24GB)
5. 充分利用48GB总显存，避免内存瓶颈
"""

import torch
import torch.nn as nn
import gc
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import psutil
import time

logger = logging.getLogger(__name__)


@dataclass
class MemoryOptimizationConfig:
    """内存优化配置"""
    # 设备分配
    teacher_device: str = "cuda:0"
    student_device: str = "cuda:1"
    
    # 内存阈值 (GB)
    gpu_memory_threshold: float = 20.0  # 单GPU内存使用阈值
    cpu_memory_threshold: float = 32.0  # CPU内存使用阈值
    
    # 优化策略
    enable_gradient_checkpointing: bool = True
    enable_mixed_precision: bool = False  # 暂时禁用，避免数值不稳定
    enable_cpu_offload: bool = False  # 是否启用CPU卸载
    
    # 清理策略
    auto_cleanup_steps: int = 50  # 自动清理间隔
    aggressive_cleanup: bool = True  # 激进清理模式
    
    # 监控配置
    enable_memory_monitoring: bool = True
    log_memory_interval: int = 100  # 内存日志间隔


class GPUMemoryOptimizer:
    """GPU内存优化器"""
    
    def __init__(self, config: MemoryOptimizationConfig):
        self.config = config
        self.device_count = torch.cuda.device_count()
        self.memory_history = []
        self.cleanup_count = 0
        
        # 初始化设备信息
        self._initialize_device_info()
        
        # 设置内存分配策略
        self._setup_memory_allocation()
        
        logger.info("GPU内存优化器初始化完成")
    
    def _initialize_device_info(self):
        """初始化设备信息"""
        self.device_info = {}
        
        logger.info("=== GPU设备信息 ===")
        for i in range(self.device_count):
            if torch.cuda.is_available():
                props = torch.cuda.get_device_properties(i)
                total_memory = props.total_memory / 1024**3
                
                self.device_info[i] = {
                    'name': props.name,
                    'total_memory': total_memory,
                    'compute_capability': f"{props.major}.{props.minor}",
                    'multiprocessor_count': getattr(props, 'multi_processor_count', 'N/A')
                }

                logger.info(f"GPU {i}: {props.name}")
                logger.info(f"  总内存: {total_memory:.1f}GB")
                logger.info(f"  计算能力: {props.major}.{props.minor}")
                # 安全获取多处理器数量
                mp_count = getattr(props, 'multi_processor_count', getattr(props, 'multiprocessor_count', 'N/A'))
                logger.info(f"  多处理器: {mp_count}")
        
        # CPU信息
        cpu_memory = psutil.virtual_memory().total / 1024**3
        logger.info(f"CPU内存: {cpu_memory:.1f}GB")
        logger.info("==================")
    
    def _setup_memory_allocation(self):
        """设置内存分配策略"""
        if torch.cuda.is_available():
            # 设置内存分配策略
            torch.cuda.set_per_process_memory_fraction(0.95, device=0)  # GPU 0使用95%
            torch.cuda.set_per_process_memory_fraction(0.95, device=1)  # GPU 1使用95%
            
            # 启用内存池
            torch.cuda.empty_cache()
            
            logger.info("内存分配策略设置完成")
    
    def get_memory_info(self, device_id: Optional[int] = None) -> Dict[str, Any]:
        """获取内存信息"""
        if device_id is None:
            # 获取所有GPU的内存信息
            info = {}
            for i in range(self.device_count):
                info[f'gpu_{i}'] = self._get_single_gpu_memory(i)
            
            # 添加CPU内存信息
            cpu_memory = psutil.virtual_memory()
            info['cpu'] = {
                'total': cpu_memory.total / 1024**3,
                'available': cpu_memory.available / 1024**3,
                'used': cpu_memory.used / 1024**3,
                'percent': cpu_memory.percent
            }
            
            return info
        else:
            return self._get_single_gpu_memory(device_id)
    
    def _get_single_gpu_memory(self, device_id: int) -> Dict[str, float]:
        """获取单个GPU的内存信息"""
        if not torch.cuda.is_available():
            return {'total': 0, 'allocated': 0, 'reserved': 0, 'free': 0}
        
        allocated = torch.cuda.memory_allocated(device_id) / 1024**3
        reserved = torch.cuda.memory_reserved(device_id) / 1024**3
        total = self.device_info.get(device_id, {}).get('total_memory', 0)
        free = total - allocated
        
        return {
            'total': total,
            'allocated': allocated,
            'reserved': reserved,
            'free': free,
            'usage_percent': (allocated / total * 100) if total > 0 else 0
        }
    
    def log_memory_status(self, step: Optional[int] = None, prefix: str = ""):
        """记录内存状态"""
        if not self.config.enable_memory_monitoring:
            return
        
        info = self.get_memory_info()
        
        step_info = f"Step {step} - " if step is not None else ""
        logger.info(f"=== {step_info}{prefix}内存状态 ===")
        
        # GPU内存
        for i in range(self.device_count):
            gpu_info = info[f'gpu_{i}']
            logger.info(f"GPU {i}: {gpu_info['allocated']:.1f}GB / {gpu_info['total']:.1f}GB "
                       f"({gpu_info['usage_percent']:.1f}%) "
                       f"[保留: {gpu_info['reserved']:.1f}GB]")
        
        # CPU内存
        cpu_info = info['cpu']
        logger.info(f"CPU: {cpu_info['used']:.1f}GB / {cpu_info['total']:.1f}GB "
                   f"({cpu_info['percent']:.1f}%)")
        
        logger.info("========================")
        
        # 记录历史
        self.memory_history.append({
            'step': step,
            'timestamp': time.time(),
            'memory_info': info
        })
        
        # 限制历史记录长度
        if len(self.memory_history) > 1000:
            self.memory_history = self.memory_history[-500:]
    
    def check_memory_pressure(self) -> Dict[str, bool]:
        """检查内存压力"""
        info = self.get_memory_info()
        pressure = {}
        
        # 检查GPU内存压力
        for i in range(self.device_count):
            gpu_info = info[f'gpu_{i}']
            pressure[f'gpu_{i}'] = gpu_info['usage_percent'] > 85.0
        
        # 检查CPU内存压力
        pressure['cpu'] = info['cpu']['percent'] > 85.0
        
        return pressure
    
    def cleanup_memory(self, aggressive: bool = None, devices: Optional[List[int]] = None):
        """清理内存"""
        if aggressive is None:
            aggressive = self.config.aggressive_cleanup
        
        if devices is None:
            devices = list(range(self.device_count))
        
        logger.info(f"执行内存清理 (激进模式: {aggressive})...")
        
        # GPU内存清理
        for device_id in devices:
            if torch.cuda.is_available():
                with torch.cuda.device(device_id):
                    torch.cuda.empty_cache()
                    if aggressive:
                        torch.cuda.synchronize()
        
        # CPU内存清理
        if aggressive:
            gc.collect()
            
            # 强制Python垃圾回收
            for _ in range(3):
                gc.collect()
        
        self.cleanup_count += 1
        logger.info(f"内存清理完成 (第{self.cleanup_count}次)")
    
    def optimize_for_training(self, model, enable_checkpointing: bool = True):
        """为训练优化模型"""
        logger.info("应用训练内存优化...")
        
        # 启用梯度检查点
        if enable_checkpointing and self.config.enable_gradient_checkpointing:
            if hasattr(model, 'gradient_checkpointing_enable'):
                model.gradient_checkpointing_enable()
                logger.info("✅ 梯度检查点已启用")
        
        # 设置模型为训练模式
        model.train()
        
        # 禁用缓存以节省内存
        if hasattr(model, 'config'):
            model.config.use_cache = False
        
        logger.info("训练内存优化完成")
    
    def optimize_for_inference(self, model):
        """为推理优化模型"""
        logger.info("应用推理内存优化...")
        
        # 设置为评估模式
        model.eval()
        
        # 启用缓存以提高推理速度
        if hasattr(model, 'config'):
            model.config.use_cache = True
        
        logger.info("推理内存优化完成")
    
    def monitor_memory_during_training(self, step: int):
        """训练期间的内存监控"""
        # 定期记录内存状态
        if step % self.config.log_memory_interval == 0:
            self.log_memory_status(step, "训练")
        
        # 检查内存压力
        pressure = self.check_memory_pressure()
        high_pressure_devices = [device for device, is_pressure in pressure.items() if is_pressure]
        
        if high_pressure_devices:
            logger.warning(f"检测到内存压力: {high_pressure_devices}")
            
            # 自动清理
            if step % self.config.auto_cleanup_steps == 0:
                self.cleanup_memory(aggressive=True)
        
        # 定期清理
        elif step % self.config.auto_cleanup_steps == 0:
            self.cleanup_memory(aggressive=False)
    
    def get_optimal_batch_size(self, model_size_gb: float, sequence_length: int = 1024) -> int:
        """计算最优批次大小"""
        # 估算单个样本的内存使用
        # 经验公式：内存使用 ≈ 模型大小 + 序列长度 * 隐藏层大小 * 4 / 1024^3
        
        available_memory = min(
            self.device_info.get(0, {}).get('total_memory', 24) * 0.8,  # GPU 0可用内存
            self.device_info.get(1, {}).get('total_memory', 24) * 0.8   # GPU 1可用内存
        )
        
        # 保守估计
        sample_memory = sequence_length * 2048 * 4 / 1024**3  # 约2GB per sample
        
        optimal_batch_size = max(1, int((available_memory - model_size_gb) / sample_memory))
        
        logger.info(f"推荐批次大小: {optimal_batch_size} "
                   f"(可用内存: {available_memory:.1f}GB, 模型: {model_size_gb:.1f}GB)")
        
        return optimal_batch_size
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用总结"""
        info = self.get_memory_info()
        
        summary = {
            'total_gpu_memory': sum(self.device_info[i]['total_memory'] for i in range(self.device_count)),
            'total_gpu_used': sum(info[f'gpu_{i}']['allocated'] for i in range(self.device_count)),
            'gpu_utilization': {},
            'cpu_memory': info['cpu'],
            'cleanup_count': self.cleanup_count,
            'optimization_recommendations': []
        }
        
        # GPU利用率
        for i in range(self.device_count):
            gpu_info = info[f'gpu_{i}']
            summary['gpu_utilization'][f'gpu_{i}'] = gpu_info['usage_percent']
        
        # 优化建议
        if summary['total_gpu_used'] / summary['total_gpu_memory'] < 0.5:
            summary['optimization_recommendations'].append("GPU内存利用率较低，可以增加批次大小")
        
        if info['cpu']['percent'] > 80:
            summary['optimization_recommendations'].append("CPU内存使用率较高，建议启用CPU卸载")
        
        return summary


# 全局内存优化器实例
memory_optimizer = None

def get_memory_optimizer(config: Optional[MemoryOptimizationConfig] = None) -> GPUMemoryOptimizer:
    """获取全局内存优化器实例"""
    global memory_optimizer
    
    if memory_optimizer is None:
        if config is None:
            config = MemoryOptimizationConfig()
        memory_optimizer = GPUMemoryOptimizer(config)
    
    return memory_optimizer


# 导出的主要接口
__all__ = [
    'MemoryOptimizationConfig',
    'GPUMemoryOptimizer',
    'get_memory_optimizer'
]
