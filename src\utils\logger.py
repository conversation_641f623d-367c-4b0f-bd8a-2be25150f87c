"""
日志工具模块

提供统一的日志配置和管理功能
"""

import logging
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import json


def setup_logger(
    name: Optional[str] = None,
    level: int = logging.INFO,
    log_dir: Optional[str] = None,
    log_file: Optional[str] = None,
    console_output: bool = True,
    file_output: bool = True,
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_dir: 日志目录
        log_file: 日志文件名
        console_output: 是否输出到控制台
        file_output: 是否输出到文件
        format_string: 自定义格式字符串
        
    Returns:
        配置好的日志记录器
    """
    # 获取或创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 设置日志格式
    if format_string is None:
        format_string = (
            '%(asctime)s - %(name)s - %(levelname)s - '
            '%(filename)s:%(lineno)d - %(message)s'
        )
    
    formatter = logging.Formatter(format_string)
    
    # 控制台输出
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件输出
    if file_output:
        # 创建日志目录
        if log_dir is None:
            log_dir = "logs"
        
        Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        # 生成日志文件名
        if log_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = f"legal_extraction_{timestamp}.log"
        
        log_path = os.path.join(log_dir, log_file)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        logger.info(f"日志文件: {log_path}")
    
    return logger


class ExperimentLogger:
    """实验日志记录器"""
    
    def __init__(self, experiment_name: str, output_dir: str):
        self.experiment_name = experiment_name
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建实验日志文件
        self.log_file = self.output_dir / f"{experiment_name}_experiment.log"
        self.metrics_file = self.output_dir / f"{experiment_name}_metrics.jsonl"
        
        # 设置日志记录器
        self.logger = setup_logger(
            name=f"experiment_{experiment_name}",
            log_dir=str(self.output_dir),
            log_file=self.log_file.name
        )
        
        # 实验开始时间
        self.start_time = datetime.now()
        self.logger.info(f"实验开始: {experiment_name}")
        self.logger.info(f"开始时间: {self.start_time}")
    
    def log_config(self, config: Dict[str, Any]):
        """记录实验配置"""
        self.logger.info("实验配置:")
        self.logger.info(json.dumps(config, indent=2, ensure_ascii=False))
        
        # 保存配置到文件
        config_file = self.output_dir / f"{self.experiment_name}_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def log_model_info(self, model_info: Dict[str, Any]):
        """记录模型信息"""
        self.logger.info("模型信息:")
        for key, value in model_info.items():
            self.logger.info(f"  {key}: {value}")
    
    def log_training_start(self, total_epochs: int, total_steps: int):
        """记录训练开始"""
        self.logger.info(f"开始训练: {total_epochs} epochs, {total_steps} steps")
    
    def log_epoch_start(self, epoch: int, total_epochs: int):
        """记录epoch开始"""
        self.logger.info(f"Epoch {epoch + 1}/{total_epochs} 开始")
    
    def log_epoch_end(self, epoch: int, metrics: Dict[str, float]):
        """记录epoch结束"""
        self.logger.info(f"Epoch {epoch + 1} 完成:")
        for key, value in metrics.items():
            self.logger.info(f"  {key}: {value:.4f}")
        
        # 保存指标到JSONL文件
        metrics_entry = {
            'epoch': epoch + 1,
            'timestamp': datetime.now().isoformat(),
            **metrics
        }
        
        with open(self.metrics_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(metrics_entry, ensure_ascii=False) + '\n')
    
    def log_best_model(self, epoch: int, metric_name: str, metric_value: float):
        """记录最佳模型"""
        self.logger.info(f"新的最佳模型 - Epoch {epoch + 1}: {metric_name} = {metric_value:.4f}")
    
    def log_training_end(self, best_metrics: Dict[str, float]):
        """记录训练结束"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        self.logger.info("训练完成!")
        self.logger.info(f"结束时间: {end_time}")
        self.logger.info(f"总耗时: {duration}")
        self.logger.info("最佳指标:")
        for key, value in best_metrics.items():
            self.logger.info(f"  {key}: {value:.4f}")
    
    def log_error(self, error: Exception, context: str = ""):
        """记录错误"""
        self.logger.error(f"错误 {context}: {str(error)}", exc_info=True)
    
    def log_warning(self, message: str):
        """记录警告"""
        self.logger.warning(message)
    
    def log_info(self, message: str):
        """记录信息"""
        self.logger.info(message)


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics_history = []
        self.best_metrics = {}
        self.current_epoch = 0
    
    def update(self, metrics: Dict[str, float], epoch: int):
        """更新指标"""
        self.current_epoch = epoch
        
        # 添加时间戳
        metrics_with_timestamp = {
            'epoch': epoch,
            'timestamp': datetime.now().isoformat(),
            **metrics
        }
        
        self.metrics_history.append(metrics_with_timestamp)
        
        # 更新最佳指标
        for key, value in metrics.items():
            if key.endswith('_loss'):
                # 损失指标：越小越好
                if key not in self.best_metrics or value < self.best_metrics[key]['value']:
                    self.best_metrics[key] = {'value': value, 'epoch': epoch}
            else:
                # 其他指标：越大越好
                if key not in self.best_metrics or value > self.best_metrics[key]['value']:
                    self.best_metrics[key] = {'value': value, 'epoch': epoch}
    
    def get_best_metric(self, metric_name: str) -> Optional[Dict[str, Any]]:
        """获取最佳指标"""
        return self.best_metrics.get(metric_name)
    
    def get_latest_metrics(self) -> Dict[str, float]:
        """获取最新指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return {}
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        if not self.metrics_history:
            return {}
        
        summary = {
            'total_epochs': len(self.metrics_history),
            'best_metrics': self.best_metrics,
            'latest_metrics': self.get_latest_metrics()
        }
        
        return summary
    
    def save_metrics(self, file_path: str):
        """保存指标历史"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump({
                'metrics_history': self.metrics_history,
                'best_metrics': self.best_metrics,
                'summary': self.get_metrics_summary()
            }, f, indent=2, ensure_ascii=False)


class ProgressLogger:
    """进度日志记录器"""
    
    def __init__(self, logger: logging.Logger, total_steps: int):
        self.logger = logger
        self.total_steps = total_steps
        self.current_step = 0
        self.start_time = datetime.now()
    
    def update(self, step: int, metrics: Optional[Dict[str, float]] = None):
        """更新进度"""
        self.current_step = step
        progress = (step / self.total_steps) * 100
        
        # 计算预估剩余时间
        elapsed_time = datetime.now() - self.start_time
        if step > 0:
            avg_time_per_step = elapsed_time.total_seconds() / step
            remaining_steps = self.total_steps - step
            eta = remaining_steps * avg_time_per_step
            eta_str = str(datetime.timedelta(seconds=int(eta)))
        else:
            eta_str = "未知"
        
        # 构建日志消息
        message = f"进度: {step}/{self.total_steps} ({progress:.1f}%) - ETA: {eta_str}"
        
        if metrics:
            metrics_str = " - ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
            message += f" - {metrics_str}"
        
        self.logger.info(message)
    
    def finish(self):
        """完成进度记录"""
        total_time = datetime.now() - self.start_time
        self.logger.info(f"完成! 总耗时: {total_time}")


def create_experiment_logger(experiment_name: str, output_dir: str) -> ExperimentLogger:
    """
    创建实验日志记录器的工厂函数
    
    Args:
        experiment_name: 实验名称
        output_dir: 输出目录
        
    Returns:
        实验日志记录器
    """
    return ExperimentLogger(experiment_name, output_dir)


if __name__ == "__main__":
    # 测试日志功能
    
    # 基本日志设置
    logger = setup_logger("test_logger", level=logging.DEBUG)
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    
    # 实验日志测试
    exp_logger = ExperimentLogger("test_experiment", "test_logs")
    
    # 记录配置
    config = {
        "model": "qwen-7b",
        "learning_rate": 2e-5,
        "batch_size": 4
    }
    exp_logger.log_config(config)
    
    # 记录训练过程
    exp_logger.log_training_start(5, 1000)
    
    for epoch in range(3):
        exp_logger.log_epoch_start(epoch, 3)
        
        # 模拟训练指标
        metrics = {
            "train_loss": 1.5 - epoch * 0.3,
            "eval_loss": 1.2 - epoch * 0.2,
            "f1": 0.6 + epoch * 0.1
        }
        
        exp_logger.log_epoch_end(epoch, metrics)
        
        if epoch == 1:
            exp_logger.log_best_model(epoch, "f1", metrics["f1"])
    
    exp_logger.log_training_end({"best_f1": 0.8, "best_epoch": 2})
    
    # 指标跟踪测试
    tracker = MetricsTracker()
    
    for epoch in range(3):
        metrics = {
            "loss": 1.0 - epoch * 0.2,
            "accuracy": 0.7 + epoch * 0.1
        }
        tracker.update(metrics, epoch)
    
    print("指标摘要:", tracker.get_metrics_summary())
