{"terminal.integrated.env.windows": {"PATH": "C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Scripts;C:\\Users\\<USER>\\anaconda3\\condabin;${env:PATH}"}, "python.defaultInterpreterPath": "C:\\Users\\<USER>\\anaconda3\\envs\\legal_ai\\python.exe", "python.terminal.activateEnvironment": true, "terminal.integrated.profiles.windows": {"Conda PowerShell": {"source": "PowerShell", "args": ["-ExecutionPolicy", "ByPass", "-NoExit", "-Command", "& 'C:\\Users\\<USER>\\anaconda3\\shell\\condabin\\conda-hook.ps1'; conda activate legal_ai"], "icon": "terminal-powershell"}}, "terminal.integrated.defaultProfile.windows": "Conda PowerShell"}