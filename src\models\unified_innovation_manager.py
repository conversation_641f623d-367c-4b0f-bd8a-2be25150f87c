"""
统一创新模块管理器 - 确保5个创新模块正确集成

这个模块统一管理：
1. HPF (Hierarchical Parameter Freezing) - 层次化参数冻结
2. LSE (Legal Semantic Enhancement) - 法律语义增强  
3. APD (Adaptive Prompt Distillation) - 自适应提示蒸馏
4. Collaborative Annotation - 协作标注
5. Partition Extraction - 分区提取

确保所有模块在统一的设备分配策略下正确工作
"""

import torch
import torch.nn as nn
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 🔧 修复：严格的模块导入，失败时直接抛出异常
try:
    from .hpf_module import HPFModule, HPFConfig
    from .apd_module import APDModule, APDConfig
    from .unified_data_preprocessing_module import UnifiedDataPreprocessingModule, UDPConfig
    logger.info("✅ 所有创新模块导入成功")
except ImportError as e:
    logger.error(f"❌ 创新模块导入失败: {e}")
    logger.error("🚨 无法继续训练，请检查模块文件是否存在")
    raise ImportError(f"创新模块导入失败，训练无法继续: {e}") from e


@dataclass
class UnifiedInnovationConfig:
    """统一创新模块配置"""
    # 模块启用开关 - 3个核心模块
    enable_hpf: bool = True
    enable_apd: bool = True
    enable_udp: bool = True  # 统一数据预处理模块

    # 设备分配策略
    primary_device: str = "cuda:1"  # 主设备（学生模型所在设备）
    secondary_device: str = "cuda:0"  # 次设备（教师模型所在设备）

    # HPF配置
    hpf_freezing_strategy: str = "hierarchical"
    hpf_entity_layers: List[int] = None
    hpf_relation_layers: List[int] = None
    hpf_reasoning_layers: List[int] = None
    hpf_adaptive_enabled: bool = True
    hpf_gradient_threshold: float = 0.01
    hpf_update_frequency: int = 100

    # APD配置 - 数值稳定性优化
    apd_temperature: float = 8.0  # 修复：进一步提高温度增加数值稳定性
    apd_alpha: float = 0.2  # 修复：进一步降低蒸馏权重
    apd_learnable_tokens: int = 8  # 修复：进一步减少可学习token数量
    apd_learning_rate: float = 5e-6  # 修复：进一步降低学习率

    # UDP配置 - 统一数据预处理模块
    udp_hidden_size: int = 2048  # 适配Qwen3-1.7B
    udp_enable_lse: bool = True  # 法律语义增强
    udp_enable_collaborative: bool = True  # 协作标注
    udp_enable_partition: bool = True  # 分区提取
    udp_num_annotators: int = 3
    udp_confidence_threshold: float = 0.8
    udp_num_partitions: int = 4
    udp_overlap_ratio: float = 0.1
    udp_feature_fusion_method: str = "attention"
    
    def __post_init__(self):
        """初始化后处理"""
        # 修复：设置适配Qwen3-1.7B (28层)的HPF层配置
        if self.hpf_entity_layers is None:
            self.hpf_entity_layers = [14, 15, 16, 17]  # 中间层用于实体识别
        if self.hpf_relation_layers is None:
            self.hpf_relation_layers = [18, 19, 20, 21]  # 较高层用于关系抽取
        if self.hpf_reasoning_layers is None:
            self.hpf_reasoning_layers = [24, 25, 26, 27]  # 最高层用于推理


class UnifiedInnovationManager:
    """
    统一创新模块管理器 - 负责协调所有创新模块

    职责：
    1. 初始化和管理3个创新模块：HPF、APD、UDP
    2. 提供统一的调用接口给训练脚本
    3. 协调模块间的数据流和设备分配
    4. 监控模块状态和性能

    模块说明：
    - HPF: 层次化参数冻结
    - APD: 自适应提示蒸馏
    - UDP: 统一数据预处理（包含LSE+协作标注+分区提取）

    注意：本管理器是创新模块的协调者，不直接处理数据，而是调用具体模块
    """
    
    def __init__(self, 
                 config: UnifiedInnovationConfig,
                 teacher_model,
                 student_model, 
                 tokenizer):
        """
        初始化统一创新模块管理器
        
        Args:
            config: 统一创新配置
            teacher_model: 教师模型
            student_model: 学生模型
            tokenizer: 分词器
        """
        self.config = config
        self.teacher_model = teacher_model
        self.student_model = student_model
        self.tokenizer = tokenizer
        
        # 获取设备信息
        self.teacher_device = next(teacher_model.parameters()).device
        self.student_device = next(student_model.parameters()).device
        self.primary_device = self.student_device  # 主设备跟随学生模型
        
        # 模块存储
        self.modules = {}
        self.module_configs = {}
        
        # 初始化所有模块
        self._initialize_all_modules()
        
        # 验证模块状态
        self._validate_modules()
    
    def _initialize_all_modules(self):
        """初始化所有3个创新模块"""
        logger.info("=== 初始化统一创新模块管理器 ===")

        # 1. HPF模块 - 层次化参数冻结
        if self.config.enable_hpf:
            self._init_hpf_module()

        # 2. APD模块 - 自适应提示蒸馏
        if self.config.enable_apd:
            self._init_apd_module()

        # 3. UDP模块 - 统一数据预处理
        if self.config.enable_udp:
            self._init_udp_module()

        # 记录初始化结果
        self._log_initialization_summary()
    
    def _init_hpf_module(self):
        """初始化HPF模块"""
        try:
            logger.info("初始化HPF模块...")
            
            hpf_config = HPFConfig(
                freezing_strategy=self.config.hpf_freezing_strategy,
                entity_layers=self.config.hpf_entity_layers,
                relation_layers=self.config.hpf_relation_layers,
                reasoning_layers=self.config.hpf_reasoning_layers,
                adaptive_enabled=self.config.hpf_adaptive_enabled,
                gradient_threshold=self.config.hpf_gradient_threshold,
                update_frequency=self.config.hpf_update_frequency
            )
            
            self.modules['hpf'] = HPFModule(
                config=hpf_config,
                model=self.student_model
            )
            self.module_configs['hpf'] = hpf_config
            
            logger.info("✅ HPF模块初始化成功")
            
        except Exception as e:
            logger.error(f"❌ HPF模块初始化失败: {e}")
            logger.error("🚨 HPF模块是核心功能，初始化失败将导致训练无法正常进行")
            raise RuntimeError(f"HPF模块初始化失败: {e}") from e
    
    def _init_udp_module(self):
        """初始化UDP模块 - 统一数据预处理"""
        try:
            logger.info("初始化UDP模块...")

            # 获取学生模型的隐藏层大小
            hidden_size = getattr(self.student_model.config, 'hidden_size', 2048)

            udp_config = UDPConfig(
                hidden_size=hidden_size,
                enable_legal_semantic_enhancement=self.config.udp_enable_lse,
                enable_collaborative_annotation=self.config.udp_enable_collaborative,
                enable_partition_extraction=self.config.udp_enable_partition,
                num_annotators=self.config.udp_num_annotators,
                confidence_threshold=self.config.udp_confidence_threshold,
                num_partitions=self.config.udp_num_partitions,
                overlap_ratio=self.config.udp_overlap_ratio,
                feature_fusion_method=self.config.udp_feature_fusion_method
            )

            self.modules['udp'] = UnifiedDataPreprocessingModule(
                config=udp_config
            ).to(self.primary_device)  # 确保在正确设备上

            self.module_configs['udp'] = udp_config

            logger.info(f"✅ UDP模块初始化成功 -> {self.primary_device}")
            logger.info(f"   - LSE: {udp_config.enable_legal_semantic_enhancement}")
            logger.info(f"   - 协作标注: {udp_config.enable_collaborative_annotation}")
            logger.info(f"   - 分区提取: {udp_config.enable_partition_extraction}")

        except Exception as e:
            logger.error(f"❌ UDP模块初始化失败: {e}")
            logger.error("🚨 UDP模块是核心功能，初始化失败将导致训练无法正常进行")
            raise RuntimeError(f"UDP模块初始化失败: {e}") from e
    
    def _init_apd_module(self):
        """初始化APD模块"""
        try:
            logger.info("初始化APD模块...")

            # 检查必要的组件
            if self.student_model is None:
                raise ValueError("学生模型未初始化")
            if self.teacher_model is None:
                raise ValueError("教师模型未初始化")
            if self.tokenizer is None:
                raise ValueError("分词器未初始化")

            apd_config = APDConfig(
                temperature=self.config.apd_temperature,
                alpha=self.config.apd_alpha,
                learnable_tokens=self.config.apd_learnable_tokens,
                learning_rate=self.config.apd_learning_rate
            )

            logger.info(f"APD配置: temperature={apd_config.temperature}, alpha={apd_config.alpha}")
            logger.info(f"设备: {self.primary_device}")

            self.modules['apd'] = APDModule(
                config=apd_config,
                student_model=self.student_model,
                teacher_model=self.teacher_model,
                tokenizer=self.tokenizer,
                device=self.primary_device  # 🚨 关键修复：传递设备参数
            )
            self.module_configs['apd'] = apd_config

            logger.info("✅ APD模块初始化成功")

        except Exception as e:
            import traceback
            logger.error(f"❌ APD模块初始化失败: {e}")
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            logger.error("🚨 APD模块是核心功能，初始化失败将导致训练无法正常进行")
            raise RuntimeError(f"APD模块初始化失败: {e}") from e
    

    
    def _log_initialization_summary(self):
        """记录初始化总结"""
        logger.info("=== 创新模块初始化总结 ===")
        
        success_count = 0
        for name, module in self.modules.items():
            if module is not None:
                status = "✅ 成功"
                success_count += 1
                # 记录模块设备位置
                if hasattr(module, 'parameters'):
                    try:
                        device = next(module.parameters()).device
                        status += f" ({device})"
                    except:
                        pass
            else:
                status = "❌ 失败"
            
            logger.info(f"  {name.upper()}模块: {status}")
        
        logger.info(f"成功初始化: {success_count}/3 个模块")

        if success_count < 3:
            logger.warning("⚠️ 部分模块初始化失败，可能影响训练效果和学术发表质量")
            logger.warning("⚠️ 建议检查模块依赖和配置")
        else:
            logger.info("🎉 所有3个创新模块初始化成功！")
    
    def _validate_modules(self):
        """验证模块状态"""
        logger.info("验证创新模块状态...")
        
        # 检查设备一致性
        for name, module in self.modules.items():
            if module is not None and hasattr(module, 'parameters'):
                try:
                    module_device = next(module.parameters()).device
                    if module_device != self.primary_device and name not in ['hpf', 'apd']:
                        logger.warning(f"{name}模块设备不一致: {module_device} vs {self.primary_device}")
                except:
                    pass
        
        logger.info("✅ 模块状态验证完成")

    def apply_hpf_freezing(self, epoch: int = 0, total_epochs: int = 3) -> bool:
        """应用HPF参数冻结策略"""
        if self.modules['hpf'] is None:
            return False

        try:
            self.modules['hpf'].update_freeze_strategy(epoch, total_epochs)
            return True
        except Exception as e:
            logger.error(f"HPF冻结策略应用失败: {e}")
            return False

    def apply_progressive_hpf_freezing(self, epoch: int, total_epochs: int,
                                     freeze_ratio: float, preserve_distillation_layers: bool = True) -> bool:
        """
        应用与知识蒸馏协同的渐进式HPF冻结策略

        Args:
            epoch: 当前训练轮次
            total_epochs: 总训练轮次
            freeze_ratio: 目标冻结比例
            preserve_distillation_layers: 是否保护知识蒸馏关键层

        Returns:
            是否成功应用冻结策略
        """
        if self.modules['hpf'] is None:
            logger.warning("HPF模块未初始化，无法应用渐进式冻结")
            return False

        try:
            frozen_count, total_params = self.modules['hpf'].apply_progressive_hpf_freezing(
                epoch=epoch,
                total_epochs=total_epochs,
                freeze_ratio=freeze_ratio,
                preserve_distillation_layers=preserve_distillation_layers
            )

            logger.debug(f"✅ 渐进式HPF冻结成功: {frozen_count}/{total_params} 参数被冻结")
            return True

        except Exception as e:
            logger.error(f"渐进式HPF冻结策略应用失败: {e}")
            return False



    def compute_apd_loss(self, student_logits: torch.Tensor, teacher_logits: torch.Tensor,
                        inputs: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """计算APD自适应蒸馏损失"""
        if self.modules['apd'] is None:
            logger.error("🚨 APD模块未初始化，无法计算蒸馏损失")
            raise RuntimeError("APD模块未初始化，训练无法继续")

        try:
            result = self.modules['apd'].compute_adaptive_distillation_loss(
                student_logits, teacher_logits, inputs
            )

            # � 修复：严格验证结果有效性
            if result is None:
                logger.error("🚨 APD模块返回None结果")
                raise RuntimeError("APD模块计算失败，返回None结果")

            if 'enhanced_loss' not in result:
                logger.error("🚨 APD结果缺少enhanced_loss字段")
                raise RuntimeError("APD模块返回的结果格式错误，缺少enhanced_loss字段")

            enhanced_loss = result['enhanced_loss']
            if not isinstance(enhanced_loss, torch.Tensor):
                logger.error(f"🚨 enhanced_loss不是tensor: {type(enhanced_loss)}")
                return None

            if torch.isnan(enhanced_loss) or torch.isinf(enhanced_loss):
                logger.error(f"🚨 enhanced_loss为NaN/Inf: {enhanced_loss}")
                return None

            logger.debug(f"APD损失计算成功: {enhanced_loss.item():.4f}")
            return result

        except Exception as e:
            logger.error(f"🚨 APD损失计算异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def compute_hpf_loss(self, model, current_epoch: int = 0, total_epochs: int = 1) -> torch.Tensor:
        """计算HPF正则化损失 - 新增的第4个损失组件"""
        if self.modules['hpf'] is None:
            logger.error("🚨 HPF模块未初始化，无法计算HPF损失")
            raise RuntimeError("HPF模块未初始化，训练无法继续")

        try:
            hpf_loss = self.modules['hpf'].compute_hpf_loss(
                model=model,
                current_epoch=current_epoch,
                total_epochs=total_epochs
            )

            logger.debug(f"✅ HPF损失计算完成: {hpf_loss.item():.4f}")
            return hpf_loss

        except Exception as e:
            logger.error(f"🚨 HPF损失计算失败: {e}")
            raise RuntimeError(f"HPF损失计算失败: {e}") from e

    def apply_udp_processing(self, text: str, hidden_states: Optional[torch.Tensor] = None,
                            tokenizer=None) -> Dict[str, Any]:
        """
        统一UDP数据预处理接口

        Args:
            text: 输入文本
            hidden_states: 可选的隐藏状态
            tokenizer: 分词器

        Returns:
            包含所有UDP处理结果的字典
        """
        if self.modules.get('udp') is None:
            logger.error("� UDP模块未初始化，无法进行数据预处理")
            raise RuntimeError("UDP模块未初始化，训练无法继续")

        try:
            # 确保数据在正确设备上
            if hidden_states is not None and hidden_states.device != self.primary_device:
                hidden_states = hidden_states.to(self.primary_device)

            # 统一调用UDP模块
            udp_results = self.modules['udp'].forward(
                input_text=text,
                tokenizer=tokenizer or self.tokenizer,
                hidden_states=hidden_states
            )

            return {
                'enhanced_text': udp_results.get('enhanced_text', text),  # 🔧 修复：添加enhanced_text
                'lse_features': udp_results.get('enhanced_features', {}),
                'collaborative_result': udp_results.get('annotations', {}),
                'partition_result': udp_results.get('partitions', []),
                'enhanced_hidden_states': udp_results.get('final_representation', hidden_states),
                'enhanced_input_ids': udp_results.get('enhanced_input_ids'),
                'enhanced_attention_mask': udp_results.get('enhanced_attention_mask'),
                'full_udp_results': udp_results
            }

        except Exception as e:
            logger.debug(f"UDP统一处理失败: {e}")
            return {
                'enhanced_text': text,  # 🔧 修复：异常时返回原始文本
                'lse_features': {},
                'collaborative_result': None,
                'partition_result': None,
                'enhanced_hidden_states': hidden_states,
                'enhanced_input_ids': None,
                'enhanced_attention_mask': None
            }

    # 保持向后兼容的简化接口
    def apply_collaborative_annotation(self, hidden_states: torch.Tensor) -> Optional[Dict[str, Any]]:
        """应用协作标注增强 - 兼容性接口"""
        result = self.apply_udp_processing("", hidden_states)
        return result.get('collaborative_result')

    def apply_partition_extraction(self, text: str) -> Optional[List[Dict[str, Any]]]:
        """应用分区提取优化 - 兼容性接口"""
        result = self.apply_udp_processing(text)
        return result.get('partition_result')

    def enhance_with_lse(self, text: str, hidden_states: torch.Tensor) -> Optional[torch.Tensor]:
        """应用LSE语义增强 - 兼容性接口"""
        if self.modules.get('udp') is None:
            return hidden_states

        try:
            # 使用UDP模块的新接口
            enhanced_hidden = self.modules['udp'].enhance_with_legal_semantics(text, hidden_states)
            return enhanced_hidden
        except Exception as e:
            logger.warning(f"LSE语义增强失败: {e}")
            return hidden_states

    def extract_legal_features(self, text: str) -> Dict[str, Any]:
        """提取法律特征 - 兼容性接口"""
        result = self.apply_udp_processing(text)
        return result.get('lse_features', {})

    def preprocess_batch_data(self, batch_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        批量数据预处理 - 新的主要接口

        这是训练脚本应该使用的主要接口，用于预处理整个batch的数据

        Args:
            batch_data: 批量数据字典

        Returns:
            预处理后的批量数据
        """
        if self.modules.get('udp') is None:
            logger.warning("UDP模块未初始化，返回原始数据")
            return batch_data

        try:
            # 调用UDP模块的主要预处理接口
            processed_data = self.modules['udp'].preprocess_for_model(
                batch_data=batch_data,
                tokenizer=self.tokenizer
            )

            logger.debug("✅ 批量数据UDP预处理完成")
            return processed_data

        except Exception as e:
            logger.error(f"批量数据UDP预处理失败: {e}")
            return batch_data

    def get_hpf_freezing_stats(self) -> Dict[str, Any]:
        """获取HPF参数冻结统计"""
        if self.modules['hpf'] is None:
            return {}

        try:
            total_params = 0
            frozen_params = 0

            for name, param in self.student_model.named_parameters():
                total_params += 1
                if not param.requires_grad:
                    frozen_params += 1

            freeze_ratio = frozen_params / total_params if total_params > 0 else 0

            # 获取各层类型的冻结情况
            layer_stats = {}
            layer_types = {
                'entity_layers': self.config.hpf_entity_layers,
                'relation_layers': self.config.hpf_relation_layers,
                'reasoning_layers': self.config.hpf_reasoning_layers
            }

            for layer_type, layer_indices in layer_types.items():
                layer_frozen = 0
                layer_total = 0

                for name, param in self.student_model.named_parameters():
                    try:
                        layer_idx = self.modules['hpf']._extract_layer_index(name)
                        if layer_idx in layer_indices:
                            layer_total += 1
                            if not param.requires_grad:
                                layer_frozen += 1
                    except:
                        continue

                if layer_total > 0:
                    layer_ratio = layer_frozen / layer_total
                    layer_stats[layer_type] = {
                        'frozen': layer_frozen,
                        'total': layer_total,
                        'ratio': layer_ratio
                    }

            return {
                'total_frozen': frozen_params,
                'total_params': total_params,
                'freeze_ratio': freeze_ratio,
                'layer_stats': layer_stats
            }

        except Exception as e:
            logger.error(f"HPF统计获取失败: {e}")
            return {}

    def log_module_status(self, step: int = 0):
        """记录模块应用状态"""
        logger.info(f"=== 创新模块状态 (Step {step}) ===")

        # HPF状态
        if self.modules['hpf'] is not None:
            stats = self.get_hpf_freezing_stats()
            if stats:
                logger.info(f"HPF: {stats['total_frozen']}/{stats['total_params']} "
                           f"参数冻结 ({stats['freeze_ratio']:.2%})")
                for layer_type, layer_stat in stats['layer_stats'].items():
                    logger.info(f"  {layer_type}: {layer_stat['frozen']}/{layer_stat['total']} "
                               f"({layer_stat['ratio']:.2%})")
            else:
                logger.info("HPF: ✅ 已启用")
        else:
            logger.info("HPF: ❌ 未启用")

        # APD模块状态
        if self.modules.get('apd') is not None:
            logger.info("APD: ✅ 已启用 - 自适应提示蒸馏")
        else:
            logger.info("APD: ❌ 未启用")

        # UDP模块状态
        if self.modules.get('udp') is not None:
            udp_config = self.module_configs.get('udp')
            logger.info("UDP: ✅ 已启用 - 统一数据预处理")
            if udp_config:
                logger.info(f"  - LSE (法律语义增强): {'✅' if udp_config.enable_legal_semantic_enhancement else '❌'}")
                logger.info(f"  - 协作标注: {'✅' if udp_config.enable_collaborative_annotation else '❌'}")
                logger.info(f"  - 分区提取: {'✅' if udp_config.enable_partition_extraction else '❌'}")
        else:
            logger.info("UDP: ❌ 未启用")

        logger.info("================================")

    def get_module_summary(self) -> Dict[str, Any]:
        """获取模块总结信息"""
        summary = {
            'total_modules': 3,  # HPF, APD, UDP
            'enabled_modules': sum(1 for module in self.modules.values() if module is not None),
            'modules_status': {},
            'device_allocation': {
                'teacher_device': str(self.teacher_device),
                'student_device': str(self.student_device),
                'primary_device': str(self.primary_device)
            }
        }

        for name, module in self.modules.items():
            summary['modules_status'][name] = {
                'enabled': module is not None,
                'device': str(next(module.parameters()).device) if module is not None and hasattr(module, 'parameters') else 'N/A'
            }

        return summary


# 导出的主要接口
__all__ = [
    'UnifiedInnovationConfig',
    'UnifiedInnovationManager'
]
