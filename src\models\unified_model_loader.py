"""
统一模型加载模块 - 解决设备分配冲突和内存管理问题

这个模块提供统一的模型加载接口，确保：
1. 教师模型和学生模型的设备分配策略一致
2. GPU内存使用最优化
3. 使用bfloat16精度获得最佳性能
4. 兼容双RTX 4090环境
"""

import os
import torch
import logging
from typing import Tuple, Optional, Dict, Any
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    AutoModel,  # 添加基础模型类
    AutoConfig
)
from dataclasses import dataclass

# 🔧 修复：导入统一的内存管理器，避免重复功能
try:
    from .gpu_memory_optimizer import get_memory_optimizer, MemoryOptimizationConfig
except ImportError:
    # 兼容不同的导入路径
    from gpu_memory_optimizer import get_memory_optimizer, MemoryOptimizationConfig

logger = logging.getLogger(__name__)


@dataclass
class UnifiedModelConfig:
    """统一模型配置类"""
    # 模型路径
    teacher_model_path: str = "/root/autodl-tmp/legal_ai_project/models/qwen3/Qwen3-8B-Base"
    student_model_path: str = "/root/autodl-tmp/legal_ai_project/models/qwen3/Qwen3-1.7B-Base"
    
    # 设备分配策略 - 统一标准
    teacher_device: str = "cuda:0"  # 教师模型固定在GPU 0
    student_device: str = "cuda:1"  # 学生模型固定在GPU 1
    
    # 精度配置：RTX 4090显存充足，使用bfloat16获得最佳性能
    use_teacher_quantization: bool = False  # RTX 4090 24GB足够，使用完整精度
    use_student_quantization: bool = False  # 学生模型使用bfloat16精度
    torch_dtype: str = "bfloat16"  # 🔧 修复：统一使用bfloat16，与Qwen3模型原生精度一致
    
    # 模型配置
    trust_remote_code: bool = True
    low_cpu_mem_usage: bool = True
    
    # 本地测试模式
    use_mock_models: bool = False  # 本地测试时使用模拟模型
    mock_teacher_hidden_size: int = 512  # 减小模型以适应本地GPU
    mock_student_hidden_size: int = 256
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        # 检查GPU可用性
        if not torch.cuda.is_available():
            logger.error("CUDA不可用，无法使用GPU")
            return False
            
        gpu_count = torch.cuda.device_count()
        logger.info(f"检测到 {gpu_count} 个GPU")
        
        # 检查双GPU配置
        if gpu_count < 2:
            logger.warning("检测到单GPU环境，调整设备分配策略")
            self.teacher_device = "cuda:0"
            self.student_device = "cuda:0"
            # 🚨 根本修复：即使单GPU也不使用量化，RTX4090 24GB足够
            logger.info("单GPU环境：RTX4090 24GB显存足够运行完整精度模型")
            
        # 检查模型路径
        if not self.use_mock_models:
            if not os.path.exists(self.teacher_model_path):
                logger.warning(f"教师模型路径不存在: {self.teacher_model_path}")
                logger.info("将使用模拟模型进行本地测试")
                self.use_mock_models = True
                
            if not os.path.exists(self.student_model_path):
                logger.warning(f"学生模型路径不存在: {self.student_model_path}")
                logger.info("将使用模拟模型进行本地测试")
                self.use_mock_models = True
        
        # 记录最终配置
        logger.info("=== 统一模型配置 ===")
        logger.info(f"教师模型: {self.teacher_model_path} -> {self.teacher_device}")
        logger.info(f"学生模型: {self.student_model_path} -> {self.student_device}")
        logger.info(f"数据类型: {self.torch_dtype}")
        logger.info(f"模拟模型模式: {self.use_mock_models}")
        logger.info("=====================")
        
        return True


# 🔧 修复：使用统一的内存管理器实例
def get_unified_memory_manager():
    """获取统一的内存管理器实例"""
    try:
        # 使用默认配置创建内存管理器
        config = MemoryOptimizationConfig()
        return get_memory_optimizer(config)
    except Exception as e:
        logger.warning(f"无法创建内存管理器: {e}")
        return None

# 全局统一内存管理器实例
memory_manager = get_unified_memory_manager()


class MockQwenModel(torch.nn.Module):
    """模拟Qwen模型 - 用于本地测试"""
    
    def __init__(self, hidden_size: int = 2048, vocab_size: int = 32000, num_layers: int = 12):
        super().__init__()
        self.hidden_size = hidden_size
        self.vocab_size = vocab_size
        self.num_layers = num_layers
        
        # 创建模拟配置
        self.config = type('MockConfig', (), {
            'hidden_size': hidden_size,
            'vocab_size': vocab_size,
            'num_hidden_layers': num_layers,
            'num_attention_heads': 16,
            'intermediate_size': hidden_size * 4,
            'max_position_embeddings': 4096,
            'use_cache': False,
            'torch_dtype': torch.bfloat16   # 修复：使用bfloat16保持一致性
        })()
        
        # 模型层
        self.embeddings = torch.nn.Embedding(vocab_size, hidden_size)
        self.layers = torch.nn.ModuleList([
            torch.nn.TransformerEncoderLayer(
                d_model=hidden_size,
                nhead=16,
                dim_feedforward=hidden_size * 4,
                dropout=0.1,
                batch_first=True
            ) for _ in range(num_layers)
        ])
        self.layer_norm = torch.nn.LayerNorm(hidden_size)
        self.lm_head = torch.nn.Linear(hidden_size, vocab_size, bias=False)
        
        logger.info(f"创建模拟Qwen模型: {hidden_size}维, {num_layers}层, {vocab_size}词汇表")
    
    def forward(self, input_ids, attention_mask=None, output_hidden_states=False, **kwargs):
        """前向传播"""
        batch_size, seq_len = input_ids.shape
        
        # 嵌入
        hidden_states = self.embeddings(input_ids)
        
        # 存储隐藏状态
        all_hidden_states = [hidden_states] if output_hidden_states else None
        
        # 注意力掩码处理
        if attention_mask is not None:
            attention_mask = attention_mask.float()
            attention_mask = (1.0 - attention_mask) * -10000.0
        
        # Transformer层
        for layer in self.layers:
            hidden_states = layer(hidden_states, src_key_padding_mask=attention_mask)
            if output_hidden_states:
                all_hidden_states.append(hidden_states)
        
        # 最终层归一化
        hidden_states = self.layer_norm(hidden_states)
        
        # 语言模型头
        logits = self.lm_head(hidden_states)
        
        # 构建输出
        output = type('MockOutput', (), {
            'logits': logits,
            'hidden_states': all_hidden_states if output_hidden_states else None,
            'last_hidden_state': hidden_states
        })()
        
        return output
    
    def num_parameters(self):
        """返回参数数量"""
        return sum(p.numel() for p in self.parameters())
    
    def gradient_checkpointing_enable(self):
        """启用梯度检查点（模拟）"""
        logger.info("模拟模型：启用梯度检查点")
        pass
    
    def save_pretrained(self, save_directory):
        """保存模型（模拟）"""
        os.makedirs(save_directory, exist_ok=True)
        torch.save(self.state_dict(), os.path.join(save_directory, "pytorch_model.bin"))
        
        # 保存配置
        config_dict = {
            'hidden_size': self.hidden_size,
            'vocab_size': self.vocab_size,
            'num_hidden_layers': self.num_layers,
            'model_type': 'mock_qwen'
        }
        
        import json
        with open(os.path.join(save_directory, "config.json"), 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        logger.info(f"模拟模型已保存到: {save_directory}")


class MockQwenTokenizer:
    """模拟Qwen Tokenizer"""

    def __init__(self, vocab_size: int = 32000):
        self.vocab_size = vocab_size
        self.pad_token = "<pad>"
        self.eos_token = "<eos>"
        self.pad_token_id = 0
        self.eos_token_id = 1

        logger.info(f"创建模拟Tokenizer: 词汇表大小 {vocab_size}")

    def __call__(self, text, max_length=None, padding=False, truncation=False, return_tensors=None, **kwargs):
        """模拟tokenize过程"""
        if isinstance(text, str):
            text = [text]

        batch_encoding = {}
        input_ids = []
        attention_mask = []

        for t in text:
            # 模拟tokenization
            length = min(len(t.split()), max_length if max_length else 128)
            length = max(length, 10)  # 至少10个token

            # 生成一致的token ids
            import hashlib
            hash_obj = hashlib.md5(t.encode())
            seed = int(hash_obj.hexdigest()[:8], 16)
            torch.manual_seed(seed)

            tokens = torch.randint(2, self.vocab_size, (length,)).tolist()
            mask = [1] * length

            # 填充或截断
            if max_length:
                if len(tokens) < max_length:
                    if padding:
                        tokens.extend([self.pad_token_id] * (max_length - len(tokens)))
                        mask.extend([0] * (max_length - len(mask)))
                elif len(tokens) > max_length and truncation:
                    tokens = tokens[:max_length]
                    mask = mask[:max_length]

            input_ids.append(tokens)
            attention_mask.append(mask)

        batch_encoding['input_ids'] = input_ids
        batch_encoding['attention_mask'] = attention_mask

        if return_tensors == 'pt':
            batch_encoding['input_ids'] = torch.tensor(batch_encoding['input_ids'])
            batch_encoding['attention_mask'] = torch.tensor(batch_encoding['attention_mask'])

        return batch_encoding

    def decode(self, token_ids, skip_special_tokens=True):
        """模拟解码过程"""
        if isinstance(token_ids, torch.Tensor):
            token_ids = token_ids.tolist()
        return f"模拟解码文本_{len(token_ids)}tokens"

    def save_pretrained(self, save_directory):
        """保存tokenizer（模拟）"""
        os.makedirs(save_directory, exist_ok=True)

        tokenizer_config = {
            'vocab_size': self.vocab_size,
            'pad_token': self.pad_token,
            'eos_token': self.eos_token,
            'tokenizer_type': 'mock_qwen'
        }

        import json
        with open(os.path.join(save_directory, "tokenizer_config.json"), 'w') as f:
            json.dump(tokenizer_config, f, indent=2)

        logger.info(f"模拟Tokenizer已保存到: {save_directory}")

    @classmethod
    def from_pretrained(cls, model_path, **kwargs):
        """从预训练路径加载（模拟）"""
        return cls()

    def apply_chat_template(self, messages, tokenize=False, add_generation_prompt=True, enable_thinking=False, **kwargs):
        """模拟聊天模板应用"""
        if isinstance(messages, list) and len(messages) > 0:
            content = messages[-1].get('content', 'test')
        else:
            content = 'test'

        template = f"<|im_start|>user\n{content}<|im_end|>\n<|im_start|>assistant\n"

        if tokenize:
            return self(template, **kwargs)
        return template


def load_unified_models(config) -> Tuple[Any, Any, Any]:
    """
    统一模型加载函数 - 解决所有设备分配冲突

    Args:
        config: 统一模型配置

    Returns:
        (teacher_model, student_model, tokenizer)
    """
    logger.info("=== 开始统一模型加载 ===")

    # 验证配置
    if not config.validate():
        raise ValueError("模型配置验证失败")

    # 记录内存状态
    if memory_manager:
        memory_manager.log_memory_status(prefix="模型加载前")

    # 加载tokenizer
    tokenizer = _load_unified_tokenizer(config)

    # 加载模型
    if config.use_mock_models:
        teacher_model, student_model = _load_mock_models(config)
    else:
        teacher_model, student_model = _load_real_models(config)

    # 最终内存状态检查
    if memory_manager:
        memory_manager.log_memory_status(prefix="模型加载完成")

    logger.info("=== 统一模型加载完成 ===")
    return teacher_model, student_model, tokenizer


def _load_unified_tokenizer(config: UnifiedModelConfig):
    """加载统一tokenizer"""
    logger.info("加载统一Tokenizer...")

    if config.use_mock_models:
        return MockQwenTokenizer()

    try:
        tokenizer = AutoTokenizer.from_pretrained(
            config.student_model_path,  # 使用学生模型路径
            trust_remote_code=config.trust_remote_code
        )

        # 设置特殊token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        logger.info("✅ 真实Tokenizer加载成功")
        return tokenizer

    except Exception as e:
        logger.warning(f"真实Tokenizer加载失败: {e}")
        logger.info("回退到模拟Tokenizer")
        return MockQwenTokenizer()


def _load_mock_models(config: UnifiedModelConfig) -> Tuple[MockQwenModel, MockQwenModel]:
    """加载模拟模型"""
    logger.info("加载模拟模型进行本地测试...")

    # 创建教师模型（更大）
    teacher_model = MockQwenModel(
        hidden_size=config.mock_teacher_hidden_size,
        vocab_size=1000,  # 减小词汇表
        num_layers=6      # 减少层数
    ).to(config.teacher_device)

    # 创建学生模型（较小）
    student_model = MockQwenModel(
        hidden_size=config.mock_student_hidden_size,
        vocab_size=1000,  # 减小词汇表
        num_layers=3      # 减少层数
    ).to(config.student_device)

    # 冻结教师模型
    for param in teacher_model.parameters():
        param.requires_grad = False
    teacher_model.eval()

    logger.info(f"✅ 模拟教师模型: {teacher_model.num_parameters()/1e6:.1f}M参数 -> {config.teacher_device}")
    logger.info(f"✅ 模拟学生模型: {student_model.num_parameters()/1e6:.1f}M参数 -> {config.student_device}")

    return teacher_model, student_model


def _load_real_models(config: UnifiedModelConfig) -> Tuple[Any, Any]:
    """加载真实模型 - 优化的设备分配和内存管理"""
    logger.info("加载真实Qwen模型...")

    torch_dtype = getattr(torch, config.torch_dtype)

    # 1. 加载教师模型到GPU 0（使用量化）
    logger.info(f"加载教师模型到 {config.teacher_device}...")

    # 🚨 根本修复：RTX4090 24GB显存足够，不需要内存检查和量化
    required_memory = 16.0  # GB for Qwen3-8B bfloat16
    logger.info(f"教师模型预计使用 {required_memory}GB 显存，RTX4090完全足够")

    try:
        # 🚨 根本修复：为了知识蒸馏，我们需要CausalLM的logits输出
        # 🎯 关键修复：使用bfloat16替代float16以避免NaN问题
        teacher_model = AutoModelForCausalLM.from_pretrained(
            config.teacher_model_path,
            torch_dtype=torch.bfloat16,  # 🔧 统一使用bfloat16
            device_map={"": config.teacher_device},
            # 🚨 根本修复：移除8bit量化，使用完整精度
            trust_remote_code=config.trust_remote_code,
            low_cpu_mem_usage=config.low_cpu_mem_usage
        )

        # 冻结教师模型参数
        for param in teacher_model.parameters():
            param.requires_grad = False
        teacher_model.eval()

        # 记录教师模型信息
        teacher_params = teacher_model.num_parameters() / 1e9
        logger.info(f"✅ 教师模型加载成功: {teacher_params:.1f}B参数, bfloat16精度 -> {config.teacher_device}")

    except Exception as e:
        logger.error(f"教师模型加载失败: {e}")
        raise

    # 清理内存
    if memory_manager:
        memory_manager.cleanup_memory(devices=[0])

    # 2. 加载学生模型到GPU 1
    logger.info(f"加载学生模型到 {config.student_device}...")

    # 🚨 根本修复：RTX4090双卡48GB显存完全足够
    required_memory = 3.5  # GB for Qwen3-1.7B float16
    if config.student_device == config.teacher_device:
        # 同一GPU，需要考虑教师模型占用
        required_memory += 16.0  # 教师模型float16占用
        logger.info(f"同GPU部署：总计需要 {required_memory}GB，RTX4090 24GB足够")
    else:
        logger.info(f"学生模型预计使用 {required_memory}GB 显存，RTX4090完全足够")

    try:
        # 🚨 根本架构修复：使用AutoModelForCausalLM确保有正确的lm_head
        # 知识蒸馏需要logits输出，必须使用CausalLM
        # 🎯 关键修复：使用bfloat16替代float16以避免NaN问题
        student_model = AutoModelForCausalLM.from_pretrained(
            config.student_model_path,
            torch_dtype=torch.bfloat16,  # 🔧 统一使用bfloat16
            device_map={"": config.student_device},
            # 🚨 根本修复：移除8bit量化，使用完整精度
            trust_remote_code=config.trust_remote_code,
            low_cpu_mem_usage=config.low_cpu_mem_usage
        )

        # 启用梯度检查点以节省内存
        student_model.gradient_checkpointing_enable()

        # 记录学生模型信息
        student_params = student_model.num_parameters() / 1e9
        logger.info(f"✅ 学生模型加载成功: {student_params:.1f}B参数, bfloat16精度 -> {config.student_device}")

    except Exception as e:
        logger.error(f"学生模型加载失败: {e}")
        raise

    # 最终内存清理
    if memory_manager:
        memory_manager.cleanup_memory()

    return teacher_model, student_model


def validate_model_compatibility(teacher_model, student_model, tokenizer) -> bool:
    """验证模型兼容性"""
    logger.info("验证模型兼容性...")

    try:
        # 检查设备分配
        teacher_device = next(teacher_model.parameters()).device

        # 处理不同类型的学生模型
        if hasattr(student_model, 'base_model'):
            # 如果是LegalExtractionModel，获取其base_model
            base_student_model = student_model.base_model
            student_device = next(base_student_model.parameters()).device
            student_config = base_student_model.config
        else:
            # 如果是原始模型
            base_student_model = student_model
            student_device = next(student_model.parameters()).device
            student_config = student_model.config

        logger.info(f"教师模型设备: {teacher_device}")
        logger.info(f"学生模型设备: {student_device}")

        # 检查模型配置
        teacher_config = teacher_model.config

        logger.info(f"教师模型隐藏层: {teacher_config.hidden_size}")
        logger.info(f"学生模型隐藏层: {student_config.hidden_size}")

        # 检查词汇表大小
        if hasattr(teacher_config, 'vocab_size') and hasattr(student_config, 'vocab_size'):
            if teacher_config.vocab_size != student_config.vocab_size:
                logger.warning(f"词汇表大小不匹配: 教师{teacher_config.vocab_size} vs 学生{student_config.vocab_size}")

        # 简单的前向传播测试
        test_input = torch.tensor([[1, 2, 3, 4, 5]], device=student_device)

        with torch.no_grad():
            # 学生模型测试 - 使用base_model进行测试
            student_output = base_student_model(test_input)

            # 处理不同的输出格式
            if hasattr(student_output, 'logits'):
                student_logits_shape = student_output.logits.shape
            elif isinstance(student_output, dict) and 'logits' in student_output:
                student_logits_shape = student_output['logits'].shape
            else:
                logger.warning("学生模型输出格式未知，跳过logits形状检查")
                student_logits_shape = "未知"

            logger.info(f"学生模型输出形状: {student_logits_shape}")

            # 教师模型测试
            teacher_input = test_input.to(teacher_device)
            teacher_output = teacher_model(teacher_input)

            # 处理不同的输出格式
            if hasattr(teacher_output, 'logits'):
                teacher_logits_shape = teacher_output.logits.shape
            elif isinstance(teacher_output, dict) and 'logits' in teacher_output:
                teacher_logits_shape = teacher_output['logits'].shape
            else:
                logger.warning("教师模型输出格式未知，跳过logits形状检查")
                teacher_logits_shape = "未知"

            logger.info(f"教师模型输出形状: {teacher_logits_shape}")

        logger.info("✅ 模型兼容性验证通过")
        return True

    except Exception as e:
        logger.error(f"❌ 模型兼容性验证失败: {e}")
        return False


# 导出的主要接口
__all__ = [
    'UnifiedModelConfig',
    'MockQwenModel',
    'MockQwenTokenizer',
    'load_unified_models',
    'validate_model_compatibility',
    'memory_manager'  # 🔧 修复：现在指向统一的内存管理器实例
]
