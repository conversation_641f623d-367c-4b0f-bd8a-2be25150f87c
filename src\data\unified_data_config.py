"""
统一数据处理配置 - 确保300/50/50分割和路径一致性

这个模块提供：
1. 统一的数据路径管理
2. 标准的300/50/50数据分割
3. 一致的数据预处理配置
4. 标签映射文件管理
5. 数据验证和完整性检查
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class UnifiedDataConfig:
    """统一数据配置类"""
    # 基础路径配置 - 自动检测本地或云端环境
    base_path: str = None  # 将在__post_init__中自动设置
    data_dir: str = "cloud_data"  # 统一使用cloud_data目录
    
    # 数据文件路径 - 300/50/50分割
    train_file: str = "train_small.jsonl"  # 300个样本
    val_file: str = "valid_small.jsonl"    # 50个样本（注意：实际文件名是valid_small.jsonl）
    test_file: str = "test_small.jsonl"    # 50个样本

    # 注意：我们专注于信息抽取，不需要分类映射文件
    
    # 数据预处理配置
    max_length: int = 1024  # 序列最大长度
    normalize_unicode: bool = True
    remove_special_chars: bool = False
    lowercase: bool = False
    
    # 训练配置
    batch_size: int = 1  # 小批次以节省内存
    num_workers: int = 4
    pin_memory: bool = False  # 禁用以节省内存
    
    # 注意：我们专注于信息抽取，不需要分类标签配置

    # 信息抽取配置
    extraction_fields: List[str] = None  # 将在__post_init__中设置
    extraction_task_focus: str = "structured_information_extraction"  # 明确任务重点
    
    # 验证配置
    validate_data: bool = True
    check_file_integrity: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        # 修复：自动检测并设置base_path
        if self.base_path is None:
            # 检测云端环境
            if os.path.exists('/root/autodl-tmp/legal_ai_project/'):
                self.base_path = '/root/autodl-tmp/legal_ai_project'
            else:
                # 本地环境，使用当前目录
                self.base_path = os.getcwd()

        # 设置信息抽取字段 - 与实际数据字段对应
        if self.extraction_fields is None:
            self.extraction_fields = [
                'fact',                 # 案件事实 (对应数据中的fact字段)
                'court_view',           # 法院观点 (对应数据中的court_view字段)
                'defendants',           # 被告信息 (对应数据中的defendants字段)
                'outcomes',             # 判决结果 (对应数据中的outcomes字段)
                'relevant_articles'     # 相关法条 (对应数据中的relevant_articles字段)
            ]

        # 构建完整路径
        self.data_base_path = os.path.join(self.base_path, self.data_dir)

        # 数据文件完整路径
        self.train_path = os.path.join(self.data_base_path, self.train_file)
        self.val_path = os.path.join(self.data_base_path, self.val_file)
        self.test_path = os.path.join(self.data_base_path, self.test_file)

        # 注意：专注于信息抽取，不需要映射文件路径
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        logger.info("验证统一数据配置...")
        
        # 检查基础路径
        if not os.path.exists(self.base_path):
            logger.error(f"基础路径不存在: {self.base_path}")
            return False
        
        if not os.path.exists(self.data_base_path):
            logger.error(f"数据目录不存在: {self.data_base_path}")
            return False
        
        # 检查数据文件
        data_files = [
            ("训练文件", self.train_path),
            ("验证文件", self.val_path),
            ("测试文件", self.test_path)
        ]
        
        for name, path in data_files:
            if not os.path.exists(path):
                logger.error(f"{name}不存在: {path}")
                return False
            else:
                logger.info(f"✅ {name}: {path}")
        
        # 注意：专注于信息抽取，不需要检查映射文件
        
        logger.info("✅ 统一数据配置验证通过")
        return True
    
    def get_data_paths(self) -> Dict[str, str]:
        """获取所有数据路径"""
        return {
            'train': self.train_path,
            'val': self.val_path,
            'test': self.test_path
        }
    
    def get_dataset_config(self) -> Dict[str, Any]:
        """获取数据集配置字典"""
        return {
            'max_length': self.max_length,
            'preprocessing': {
                'normalize_unicode': self.normalize_unicode,
                'remove_special_chars': self.remove_special_chars,
                'lowercase': self.lowercase
            },
            'batch_size': self.batch_size,
            'num_workers': self.num_workers,
            'pin_memory': self.pin_memory
        }


class DataValidator:
    """数据验证器"""
    
    def __init__(self, config: UnifiedDataConfig):
        self.config = config
    
    def validate_data_split(self) -> bool:
        """验证300/50/50数据分割"""
        logger.info("验证数据分割...")
        
        try:
            # 检查训练集
            train_count = self._count_lines(self.config.train_path)
            val_count = self._count_lines(self.config.val_path)
            test_count = self._count_lines(self.config.test_path)
            
            logger.info(f"数据分割统计:")
            logger.info(f"  训练集: {train_count} 样本")
            logger.info(f"  验证集: {val_count} 样本")
            logger.info(f"  测试集: {test_count} 样本")
            logger.info(f"  总计: {train_count + val_count + test_count} 样本")
            
            # 验证分割比例
            expected_train = 300
            expected_val = 50
            expected_test = 50
            
            if train_count != expected_train:
                logger.warning(f"训练集样本数不匹配: 期望{expected_train}, 实际{train_count}")
            
            if val_count != expected_val:
                logger.warning(f"验证集样本数不匹配: 期望{expected_val}, 实际{val_count}")
            
            if test_count != expected_test:
                logger.warning(f"测试集样本数不匹配: 期望{expected_test}, 实际{test_count}")
            
            # 检查是否符合预期
            total_expected = expected_train + expected_val + expected_test
            total_actual = train_count + val_count + test_count
            
            if total_actual == total_expected:
                logger.info("✅ 数据分割验证通过")
                return True
            else:
                logger.warning(f"⚠️ 数据总数不匹配: 期望{total_expected}, 实际{total_actual}")
                return False
                
        except Exception as e:
            logger.error(f"数据分割验证失败: {e}")
            return False
    
    def _count_lines(self, file_path: str) -> int:
        """计算文件行数"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except Exception as e:
            logger.error(f"无法读取文件 {file_path}: {e}")
            return 0
    
    def validate_extraction_config(self) -> bool:
        """验证信息抽取配置"""
        logger.info("验证信息抽取配置...")

        # 验证抽取字段配置
        if not self.config.extraction_fields:
            logger.error("未配置信息抽取字段")
            return False

        logger.info(f"信息抽取字段: {len(self.config.extraction_fields)} 个")
        for field in self.config.extraction_fields:
            logger.info(f"  - {field}")

        logger.info("✅ 信息抽取配置验证完成")
        return True
    
    def validate_data_format(self) -> bool:
        """验证数据格式"""
        logger.info("验证数据格式...")
        
        try:
            # 检查训练数据格式
            sample_count = 0
            with open(self.config.train_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if sample_count >= 5:  # 只检查前5个样本
                        break
                    
                    try:
                        data = json.loads(line.strip())
                        
                        # 检查信息抽取必需字段
                        required_fields = ['fact', 'court_view', 'defendants', 'outcomes']
                        for field in required_fields:
                            if field not in data:
                                logger.error(f"第{line_num}行缺少字段: {field}")
                                return False
                        
                        sample_count += 1
                        
                    except json.JSONDecodeError as e:
                        logger.error(f"第{line_num}行JSON格式错误: {e}")
                        return False
            
            logger.info(f"✅ 数据格式验证通过 (检查了{sample_count}个样本)")
            return True
            
        except Exception as e:
            logger.error(f"数据格式验证失败: {e}")
            return False
    
    def run_full_validation(self) -> bool:
        """运行完整验证"""
        logger.info("=== 开始完整数据验证 ===")
        
        validations = [
            ("数据分割", self.validate_data_split),
            ("信息抽取配置", self.validate_extraction_config),
            ("数据格式", self.validate_data_format)
        ]
        
        all_passed = True
        for name, validation_func in validations:
            try:
                result = validation_func()
                if result:
                    logger.info(f"✅ {name}验证通过")
                else:
                    logger.error(f"❌ {name}验证失败")
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ {name}验证出错: {e}")
                all_passed = False
        
        if all_passed:
            logger.info("🎉 所有数据验证通过！")
        else:
            logger.error("⚠️ 部分数据验证失败，可能影响训练效果")
        
        logger.info("=== 数据验证完成 ===")
        return all_passed


def create_unified_data_config(base_path: Optional[str] = None) -> UnifiedDataConfig:
    """创建统一数据配置"""
    if base_path is None:
        # 自动检测环境
        if os.path.exists("/root/autodl-tmp/legal_ai_project"):
            base_path = "/root/autodl-tmp/legal_ai_project"  # 云服务器环境
        else:
            base_path = "."  # 本地环境
    
    config = UnifiedDataConfig(base_path=base_path)
    
    # 验证配置
    if config.validate():
        logger.info("✅ 统一数据配置创建成功")
    else:
        logger.error("❌ 统一数据配置验证失败")
        raise ValueError("数据配置无效")
    
    return config


def validate_unified_data(config: Optional[UnifiedDataConfig] = None) -> bool:
    """验证统一数据"""
    if config is None:
        config = create_unified_data_config()
    
    validator = DataValidator(config)
    return validator.run_full_validation()


# 导出的主要接口
__all__ = [
    'UnifiedDataConfig',
    'DataValidator',
    'create_unified_data_config',
    'validate_unified_data'
]
