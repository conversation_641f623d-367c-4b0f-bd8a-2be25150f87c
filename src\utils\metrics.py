"""
评估指标模块

提供司法笔录信息提取任务的各种评估指标
"""

import numpy as np
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    classification_report,
    confusion_matrix,
    roc_auc_score,
    average_precision_score
)
from typing import Dict, List, Any, Optional, Tuple
import logging
import json

logger = logging.getLogger(__name__)


def compute_metrics(predictions: List[int], labels: List[int]) -> Dict[str, float]:
    """
    计算基本分类指标
    
    Args:
        predictions: 预测标签列表
        labels: 真实标签列表
        
    Returns:
        指标字典
    """
    metrics = {}
    
    # 基本指标
    metrics['accuracy'] = accuracy_score(labels, predictions)
    metrics['precision'] = precision_score(labels, predictions, average='weighted', zero_division=0)
    metrics['recall'] = recall_score(labels, predictions, average='weighted', zero_division=0)
    metrics['f1'] = f1_score(labels, predictions, average='weighted', zero_division=0)
    
    # 宏平均指标
    metrics['macro_precision'] = precision_score(labels, predictions, average='macro', zero_division=0)
    metrics['macro_recall'] = recall_score(labels, predictions, average='macro', zero_division=0)
    metrics['macro_f1'] = f1_score(labels, predictions, average='macro', zero_division=0)
    
    # 微平均指标
    metrics['micro_precision'] = precision_score(labels, predictions, average='micro', zero_division=0)
    metrics['micro_recall'] = recall_score(labels, predictions, average='micro', zero_division=0)
    metrics['micro_f1'] = f1_score(labels, predictions, average='micro', zero_division=0)
    
    return metrics


def compute_detailed_metrics(
    predictions: List[int], 
    labels: List[int],
    class_names: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    计算详细的分类指标
    
    Args:
        predictions: 预测标签列表
        labels: 真实标签列表
        class_names: 类别名称列表
        
    Returns:
        详细指标字典
    """
    metrics = compute_metrics(predictions, labels)
    
    # 分类报告
    if class_names:
        target_names = class_names
    else:
        unique_labels = sorted(list(set(labels + predictions)))
        target_names = [f"Class_{i}" for i in unique_labels]
    
    try:
        report = classification_report(
            labels, predictions, 
            target_names=target_names,
            output_dict=True,
            zero_division=0
        )
        metrics['classification_report'] = report
    except Exception as e:
        logger.warning(f"生成分类报告时出错: {e}")
        metrics['classification_report'] = {}
    
    # 混淆矩阵
    try:
        cm = confusion_matrix(labels, predictions)
        metrics['confusion_matrix'] = cm.tolist()
    except Exception as e:
        logger.warning(f"生成混淆矩阵时出错: {e}")
        metrics['confusion_matrix'] = []
    
    return metrics


def compute_extraction_metrics(
    predictions: List[Dict[str, Any]],
    labels: List[Dict[str, Any]]
) -> Dict[str, float]:
    """
    计算信息抽取任务的专用指标

    Args:
        predictions: 预测的信息抽取结果
        labels: 真实的信息抽取标签

    Returns:
        信息抽取指标字典
    """
    metrics = {}

    # 计算各个抽取字段的F1分数
    extraction_fields = ['defendants', 'charges', 'articles', 'court_view', 'outcomes']

    for field in extraction_fields:
        field_predictions = []
        field_labels = []

        for pred, label in zip(predictions, labels):
            pred_field = pred.get(field, [])
            label_field = label.get(field, [])

            # 转换为二进制标签（是否包含该字段信息）
            field_predictions.append(1 if pred_field else 0)
            field_labels.append(1 if label_field else 0)

        if field_labels:
            field_f1 = f1_score(field_labels, field_predictions, zero_division=0)
            metrics[f'{field}_extraction_f1'] = field_f1

    return metrics


# 删除了过时的刑罚预测指标函数，因为项目专注于信息抽取而非刑罚预测


def compute_extraction_consistency_metrics(
    predictions: List[Dict[str, Any]],
    labels: List[Dict[str, Any]]
) -> Dict[str, float]:
    """
    计算信息抽取一致性指标

    Args:
        predictions: 预测的信息抽取结果
        labels: 真实的信息抽取标签

    Returns:
        一致性指标字典
    """
    metrics = {}

    # 字段完整性一致性
    complete_extractions = 0
    total_extractions = 0

    required_fields = ['defendants', 'charges', 'articles', 'court_view']

    for pred, true in zip(predictions, labels):
        # 检查是否所有必需字段都被抽取
        pred_complete = all(pred.get(field) for field in required_fields)
        true_complete = all(true.get(field) for field in required_fields)

        if pred_complete and true_complete:
            complete_extractions += 1
        total_extractions += 1

    if total_extractions > 0:
        metrics['extraction_completeness'] = complete_extractions / total_extractions

    # 多被告信息抽取一致性
    multi_defendant_consistency = 0
    multi_defendant_cases = 0

    for pred, true in zip(predictions, labels):
        pred_defendants = pred.get('defendants', [])
        true_defendants = true.get('defendants', [])

        if len(true_defendants) > 1:
            # 检查是否正确抽取了多个被告
            pred_count = len(pred_defendants)
            true_count = len(true_defendants)

            # 允许±1的误差
            if abs(pred_count - true_count) <= 1:
                multi_defendant_consistency += 1
            multi_defendant_cases += 1

    if multi_defendant_cases > 0:
        metrics['multi_defendant_consistency'] = multi_defendant_consistency / multi_defendant_cases

    return metrics


# 删除了过时的罪名-刑罚一致性检查函数，因为项目专注于信息抽取而非分类和刑罚预测


def evaluate_extraction_performance(
    predictions: List[Dict[str, Any]],
    labels: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    综合评估信息抽取模型性能

    Args:
        predictions: 预测的信息抽取结果
        labels: 真实的信息抽取标签

    Returns:
        综合评估结果
    """
    results = {}

    # 信息抽取指标
    results['extraction_metrics'] = compute_extraction_metrics(predictions, labels)

    # 一致性指标
    results['consistency_metrics'] = compute_extraction_consistency_metrics(predictions, labels)

    # 计算整体抽取质量分数
    extraction_scores = list(results['extraction_metrics'].values())
    consistency_scores = list(results['consistency_metrics'].values())

    if extraction_scores:
        results['overall_extraction_score'] = np.mean(extraction_scores)

    if consistency_scores:
        results['overall_consistency_score'] = np.mean(consistency_scores)

    # 综合质量分数
    if extraction_scores and consistency_scores:
        results['overall_quality_score'] = (
            0.7 * results['overall_extraction_score'] +
            0.3 * results['overall_consistency_score']
        )

    return results


def save_evaluation_results(results: Dict[str, Any], output_path: str):
    """
    保存评估结果
    
    Args:
        results: 评估结果
        output_path: 输出路径
    """
    # 转换numpy数组为列表，以便JSON序列化
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    converted_results = convert_numpy(results)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(converted_results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"评估结果已保存到: {output_path}")


if __name__ == "__main__":
    # 测试信息抽取评估指标
    import random

    # 生成测试数据
    n_samples = 100
    extraction_fields = ['defendants', 'charges', 'articles', 'court_view', 'outcomes']

    # 模拟信息抽取结果
    predictions = []
    labels = []

    for _ in range(n_samples):
        pred = {}
        label = {}

        for field in extraction_fields:
            # 随机生成是否包含该字段
            pred[field] = [f"pred_{field}_{i}" for i in range(random.randint(0, 3))]
            label[field] = [f"true_{field}_{i}" for i in range(random.randint(0, 3))]

        predictions.append(pred)
        labels.append(label)

    # 计算信息抽取指标
    extraction_metrics = compute_extraction_metrics(predictions, labels)
    print("信息抽取指标:")
    for key, value in extraction_metrics.items():
        print(f"  {key}: {value:.4f}")

    # 一致性指标
    consistency_metrics = compute_extraction_consistency_metrics(predictions, labels)
    print("\n一致性指标:")
    for key, value in consistency_metrics.items():
        print(f"  {key}: {value:.4f}")

    # 综合评估
    results = evaluate_extraction_performance(predictions, labels)
    print(f"\n综合评估结果包含 {len(results)} 个部分")
    print(f"整体质量分数: {results.get('overall_quality_score', 0):.4f}")
