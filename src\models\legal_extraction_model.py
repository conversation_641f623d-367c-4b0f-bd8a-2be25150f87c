"""
司法结构化信息抽取模型 - 专注于信息抽取而非分类

这个模型专门设计用于：
1. 从司法文书中抽取结构化信息
2. 结合知识蒸馏提高抽取准确性
3. 配合创新模块管理器使用（HPF、APD、UDP模块由管理器统一调度）
4. 不进行罪名和法条分类

注意：本模型专注于抽取逻辑，创新模块的调用由UnifiedInnovationManager负责
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from transformers import PreTrainedModel, PreTrainedTokenizer

logger = logging.getLogger(__name__)


@dataclass
class ExtractionConfig:
    """结构化信息抽取配置"""
    # 抽取字段定义 - 与实际数据字段对应
    extraction_fields = [
        'fact',                 # 案件事实 (对应数据中的fact字段)
        'court_view',           # 法院观点 (对应数据中的court_view字段)
        'defendants',           # 被告信息 (对应数据中的defendants字段)
        'outcomes',             # 判决结果 (对应数据中的outcomes字段)
        'relevant_articles'     # 相关法条 (对应数据中的relevant_articles字段)
    ]
    
    # 模型配置 - 动态从base_model获取
    hidden_size: int = None  # 将从base_model.config动态获取
    max_sequence_length: int = 1024
    extraction_head_dropout: float = 0.1
    
    # 训练配置 - 修复：使用合理的损失权重
    extraction_loss_weight: float = 1.0  # 主要信息抽取权重
    distillation_loss_weight: float = 0.5  # 适度知识蒸馏权重
    semantic_loss_weight: float = 0.3  # 语义增强权重
    
    # 抽取策略 - 根本性修复碎片化问题
    extraction_method: str = "direct_extraction"  # 直接抽取，避免复杂标注
    use_crf: bool = False  # 禁用CRF
    num_labels: int = 2  # 简化为二分类：相关/不相关
    
    # 评估配置
    evaluation_metrics = [
        'extraction_f1',
        'semantic_similarity',
        'information_completeness'
    ]

    def to_dict(self):
        """转换为字典格式，兼容transformers库"""
        return {
            'hidden_size': self.hidden_size,
            'extraction_fields': self.extraction_fields,
            'max_sequence_length': self.max_sequence_length,
            'extraction_head_dropout': self.extraction_head_dropout,
            'extraction_loss_weight': self.extraction_loss_weight,
            'distillation_loss_weight': self.distillation_loss_weight,
            'semantic_loss_weight': self.semantic_loss_weight,
            'extraction_method': self.extraction_method,
            'use_crf': self.use_crf,
            'evaluation_metrics': self.evaluation_metrics
        }


class LegalExtractionHead(nn.Module):
    """司法信息抽取头"""
    
    def __init__(self, config: ExtractionConfig):
        super().__init__()
        self.config = config
        self.num_fields = len(config.extraction_fields)
        
        # 序列标注头 - 为每个抽取字段创建标注器
        self.field_extractors = nn.ModuleDict()
        
        for field in config.extraction_fields:
            # 每个字段使用BIO标注 (Begin, Inside, Outside)
            num_labels = 3  # B, I, O

            # 修复：创建数值稳定的抽取头
            extractor = nn.Sequential(
                nn.Dropout(config.extraction_head_dropout),
                nn.Linear(config.hidden_size, config.hidden_size // 4),  # 减少中间层大小
                nn.LayerNorm(config.hidden_size // 4),  # 添加LayerNorm稳定训练
                nn.GELU(),  # 使用GELU替代ReLU，更平滑
                nn.Dropout(config.extraction_head_dropout),
                nn.Linear(config.hidden_size // 4, num_labels)
            )

            # 修复：更稳定的权重初始化
            for module in extractor.modules():
                if isinstance(module, nn.Linear):
                    # 使用Xavier初始化，更适合深度网络
                    nn.init.xavier_uniform_(module.weight, gain=1.0)
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0.0)
                elif isinstance(module, nn.LayerNorm):
                    nn.init.constant_(module.weight, 1.0)
                    nn.init.constant_(module.bias, 0.0)

            # 🔧 修复：确保抽取器使用bfloat16数据类型
            extractor = extractor.to(dtype=torch.bfloat16)

            self.field_extractors[field] = extractor
        
        # CRF层（可选）- 修复数值稳定性
        if config.use_crf:
            try:
                from torchcrf import CRF
                self.crf_layers = nn.ModuleDict()
                for field in config.extraction_fields:
                    # 创建CRF层并初始化权重
                    crf = CRF(3, batch_first=True)  # 3个标签：B, I, O
                    # 初始化CRF转移矩阵，避免极值
                    if hasattr(crf, 'transitions'):
                        nn.init.normal_(crf.transitions, mean=0.0, std=0.1)
                    self.crf_layers[field] = crf
            except ImportError:
                logger.warning("torchcrf未安装，禁用CRF层")
                config.use_crf = False
        
        # 语义相似度计算
        self.semantic_projector = nn.Linear(config.hidden_size, 256)
        
        logger.info(f"初始化司法信息抽取头，支持{self.num_fields}个抽取字段")
    
    def forward(self, hidden_states: torch.Tensor, attention_mask: torch.Tensor = None,
                labels: Dict[str, torch.Tensor] = None) -> Dict[str, Any]:
        """
        前向传播 - DataParallel兼容版本

        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
            attention_mask: [batch_size, seq_len]
            labels: 各字段的标签 {field_name: [batch_size, seq_len]}
        """
        batch_size, seq_len, hidden_size = hidden_states.shape

        # DataParallel兼容性修复：统一所有tensor长度到max_sequence_length
        target_seq_len = self.config.max_sequence_length

        if seq_len != target_seq_len:
            if seq_len < target_seq_len:
                # 填充到目标长度
                pad_len = target_seq_len - seq_len
                # 填充hidden_states
                pad_hidden = torch.zeros(batch_size, pad_len, hidden_size,
                                       device=hidden_states.device, dtype=hidden_states.dtype)
                hidden_states = torch.cat([hidden_states, pad_hidden], dim=1)

                # 填充attention_mask
                if attention_mask is not None:
                    pad_mask = torch.zeros(batch_size, pad_len,
                                         device=attention_mask.device, dtype=attention_mask.dtype)
                    attention_mask = torch.cat([attention_mask, pad_mask], dim=1)

                # 填充labels
                if labels is not None:
                    for field, field_labels in labels.items():
                        if field_labels is not None:
                            pad_labels = torch.zeros(batch_size, pad_len,
                                                   device=field_labels.device, dtype=field_labels.dtype)
                            labels[field] = torch.cat([field_labels, pad_labels], dim=1)
            else:
                # 截断到目标长度
                hidden_states = hidden_states[:, :target_seq_len, :]
                if attention_mask is not None:
                    attention_mask = attention_mask[:, :target_seq_len]
                if labels is not None:
                    for field, field_labels in labels.items():
                        if field_labels is not None:
                            labels[field] = field_labels[:, :target_seq_len]

            # 更新seq_len
            seq_len = target_seq_len
        
        # 为每个字段进行序列标注
        field_logits = {}
        field_predictions = {}
        # 初始化损失为tensor，避免DataParallel问题
        extraction_loss = torch.tensor(0.0, device=hidden_states.device, requires_grad=True)
        
        # 🚨 根本修复：不要修改输入的hidden_states！
        # 创建局部副本用于抽取任务，保持原始hidden_states不变
        local_hidden_states = hidden_states.clone()

        # 对局部副本进行数值稳定化处理
        # 1. 检查并修复异常值（仅在局部副本上）
        if torch.isnan(local_hidden_states).any() or torch.isinf(local_hidden_states).any():
            logger.warning("检测到局部hidden_states数值异常，执行修复")
            # 使用layer norm进行修复，而不是随机噪声
            local_hidden_states = torch.nn.functional.layer_norm(
                local_hidden_states, local_hidden_states.shape[-1:]
            )
            # 如果还有问题，进行温和的裁剪
            if torch.isnan(local_hidden_states).any() or torch.isinf(local_hidden_states).any():
                local_hidden_states = torch.clamp(local_hidden_states, min=-5.0, max=5.0)

        # 2. 温和的数值裁剪（仅在局部副本上）
        local_hidden_states = torch.clamp(local_hidden_states, min=-10.0, max=10.0)

        for field in self.config.extraction_fields:
            # 🚨 关键修复：安全计算logits
            try:
                # 🚨 关键修复：确保数据类型匹配
                extractor = self.field_extractors[field]

                # 🔧 修复：确保数据类型一致（统一使用bfloat16）
                extractor_dtype = next(extractor.parameters()).dtype
                if local_hidden_states.dtype != extractor_dtype:
                    logger.debug(f"数据类型对齐: {local_hidden_states.dtype} -> {extractor_dtype}")
                    local_hidden_states = local_hidden_states.to(dtype=extractor_dtype)

                logits = extractor(local_hidden_states)  # [batch_size, seq_len, 3]

                # 🚨 立即检查logits数值稳定性
                if torch.isnan(logits).any() or torch.isinf(logits).any():
                    logger.error(f"🚨 字段 {field} 的logits包含NaN/Inf，重新初始化抽取器")
                    # 重新初始化该字段的抽取器
                    extractor = nn.Sequential(
                        nn.Dropout(self.config.extraction_head_dropout),
                        nn.Linear(self.config.hidden_size, self.config.hidden_size // 2),
                        nn.ReLU(),
                        nn.Dropout(self.config.extraction_head_dropout),
                        nn.Linear(self.config.hidden_size // 2, 3)
                    ).to(local_hidden_states.device)

                    # 更稳定的权重初始化
                    for module in extractor.modules():
                        if isinstance(module, nn.Linear):
                            # 使用更小的标准差初始化
                            nn.init.normal_(module.weight, mean=0.0, std=0.02)
                            if module.bias is not None:
                                nn.init.constant_(module.bias, 0.0)

                    # 🚨 关键修复：确保数据类型匹配
                    if local_hidden_states.dtype == torch.float16:
                        extractor = extractor.half()

                    self.field_extractors[field] = extractor
                    logits = extractor(local_hidden_states)

                    # 如果还是NaN，使用随机logits
                    if torch.isnan(logits).any() or torch.isinf(logits).any():
                        logger.error(f"🚨 字段 {field} 重新初始化后仍有NaN，使用随机logits")
                        logits = torch.randn_like(logits) * 0.1

                # 最终安全裁剪
                logits = torch.clamp(logits, min=-15.0, max=15.0)
                field_logits[field] = logits

            except Exception as e:
                logger.error(f"🚨 字段 {field} logits计算异常: {e}")
                # 创建安全的随机logits
                batch_size, seq_len = local_hidden_states.shape[:2]
                logits = torch.randn(batch_size, seq_len, 3, device=local_hidden_states.device) * 0.1
                field_logits[field] = logits
            
            if self.config.use_crf and hasattr(self, 'crf_layers'):
                # 使用CRF进行预测
                if labels is not None and field in labels:
                    # 训练时计算CRF损失
                    field_labels = labels[field]
                    # 确保标签在有效范围内 [0, num_labels-1]
                    field_labels = torch.clamp(field_labels, 0, self.config.num_labels - 1)

                    # 处理attention_mask的数据类型问题
                    crf_mask = None
                    if attention_mask is not None:
                        # 确保mask是布尔类型
                        if attention_mask.dtype != torch.bool:
                            crf_mask = attention_mask.bool()
                        else:
                            crf_mask = attention_mask

                    try:
                        # 🔧 关键修复：确保CRF层、logits、标签都在同一设备上
                        target_device = logits.device

                        # 确保CRF层在正确设备上
                        if hasattr(self.crf_layers[field], 'start_transitions'):
                            if self.crf_layers[field].start_transitions.device != target_device:
                                self.crf_layers[field] = self.crf_layers[field].to(target_device)
                                logger.debug(f"CRF层 {field} 已移动到设备: {target_device}")

                        # 确保标签在正确设备上
                        field_labels = field_labels.to(target_device)
                        if crf_mask is not None:
                            crf_mask = crf_mask.to(target_device)

                        # 🚀 双RTX4090激进训练：合理的logits范围
                        logits_clipped = torch.clamp(logits, min=-15.0, max=15.0)

                        # 🔧 添加logits数值稳定性检查
                        if torch.isnan(logits_clipped).any() or torch.isinf(logits_clipped).any():
                            logger.warning(f"Logits {field} 包含NaN/Inf，使用交叉熵替代")
                            # 直接使用交叉熵损失，不跳过
                            ce_loss = F.cross_entropy(
                                torch.randn_like(logits_clipped),  # 使用随机logits
                                field_labels.view(-1),
                                ignore_index=0,
                                reduction='mean'
                            )
                            ce_loss = torch.clamp(ce_loss, min=0.0, max=20.0)
                            extraction_loss = extraction_loss + ce_loss
                            continue

                        # 计算CRF损失并添加数值稳定性检查
                        try:
                            crf_loss = -self.crf_layers[field](logits_clipped, field_labels, mask=crf_mask)

                            # 🚨 关键：检查CRF损失的数值稳定性
                            if torch.isnan(crf_loss).any() or torch.isinf(crf_loss).any():
                                logger.warning(f"CRF损失 {field} 出现NaN/Inf，使用交叉熵替代")
                                # 立即回退到交叉熵损失
                                ce_loss = F.cross_entropy(
                                    logits_clipped.view(-1, logits_clipped.size(-1)),
                                    field_labels.view(-1),
                                    ignore_index=0,
                                    reduction='mean'
                                )
                                crf_loss = ce_loss

                            # 🚀 合理的损失值裁剪，允许充分学习
                            crf_loss = torch.clamp(crf_loss.mean(), min=0.0, max=20.0)
                            extraction_loss = extraction_loss + crf_loss  # 避免in-place操作
                        except Exception as crf_error:
                            logger.warning(f"CRF计算异常 {field}: {crf_error}")
                            # 直接使用交叉熵损失
                            ce_loss = F.cross_entropy(
                                logits_clipped.view(-1, logits_clipped.size(-1)),
                                field_labels.view(-1),
                                ignore_index=0,
                                reduction='mean'
                            )
                            ce_loss = torch.clamp(ce_loss, min=0.0, max=20.0)
                            extraction_loss = extraction_loss + ce_loss
                    except Exception as e:
                        logger.warning(f"CRF损失计算失败 {field}: {e}, 使用交叉熵损失")
                        # 回退到交叉熵损失 - 确保设备一致性
                        target_device = logits.device
                        field_labels = field_labels.to(target_device)
                        ce_loss = F.cross_entropy(logits.view(-1, logits.size(-1)), field_labels.view(-1), ignore_index=0)
                        extraction_loss = extraction_loss + ce_loss  # 避免in-place操作

                # CRF解码
                try:
                    # 使用相同的mask处理
                    crf_mask = None
                    if attention_mask is not None:
                        if attention_mask.dtype != torch.bool:
                            crf_mask = attention_mask.bool()
                        else:
                            crf_mask = attention_mask

                    predictions = self.crf_layers[field].decode(logits, mask=crf_mask)
                    # DataParallel兼容性：确保CRF decode结果是正确形状的tensor
                    if isinstance(predictions, list):
                        # 将列表转换为tensor，现在所有序列都应该是相同长度
                        padded_predictions = []
                        for seq in predictions:
                            # 确保序列长度正确
                            if len(seq) < seq_len:
                                padded_seq = seq + [0] * (seq_len - len(seq))
                            elif len(seq) > seq_len:
                                padded_seq = seq[:seq_len]
                            else:
                                padded_seq = seq
                            padded_predictions.append(padded_seq)
                        predictions = torch.tensor(padded_predictions, device=logits.device, dtype=torch.long)

                    # 确保tensor形状正确 [batch_size, seq_len]
                    if predictions.dim() == 1:
                        predictions = predictions.unsqueeze(0)
                    if predictions.size(1) != seq_len:
                        if predictions.size(1) < seq_len:
                            pad_size = seq_len - predictions.size(1)
                            pad_tensor = torch.zeros(predictions.size(0), pad_size,
                                                   device=predictions.device, dtype=predictions.dtype)
                            predictions = torch.cat([predictions, pad_tensor], dim=1)
                        else:
                            predictions = predictions[:, :seq_len]

                    field_predictions[field] = predictions
                except Exception as e:
                    logger.warning(f"CRF解码失败 {field}: {e}, 使用argmax")
                    predictions = torch.argmax(logits, dim=-1)
                    field_predictions[field] = predictions
            else:
                # 不使用CRF，直接softmax
                predictions = torch.argmax(logits, dim=-1)
                field_predictions[field] = predictions
                
                if labels is not None and field in labels:
                    # 🚀 双RTX4090激进训练：交叉熵损失充分学习
                    try:
                        # 合理裁剪logits
                        logits_clipped = torch.clamp(logits, min=-15.0, max=15.0)

                        # 检查logits数值稳定性
                        if torch.isnan(logits_clipped).any() or torch.isinf(logits_clipped).any():
                            logger.warning(f"交叉熵logits {field} 包含NaN/Inf，使用随机logits")
                            logits_clipped = torch.randn_like(logits_clipped) * 0.1  # 小幅随机logits

                        # 计算交叉熵损失
                        loss = F.cross_entropy(
                            logits_clipped.view(-1, 3),
                            labels[field].view(-1),
                            ignore_index=-100,
                            reduction='mean'
                        )

                        # 检查损失数值稳定性
                        if torch.isnan(loss).any() or torch.isinf(loss).any():
                            logger.warning(f"交叉熵损失 {field} 包含NaN/Inf，使用默认损失")
                            loss = torch.tensor(1.0, device=logits.device, requires_grad=True)

                        # 合理裁剪损失值，允许充分学习
                        loss = torch.clamp(loss, min=0.0, max=20.0)
                        extraction_loss = extraction_loss + loss
                    except Exception as ce_error:
                        logger.warning(f"交叉熵损失计算异常 {field}: {ce_error}")
                        # 添加默认损失而不是跳过
                        default_loss = torch.tensor(1.0, device=local_hidden_states.device, requires_grad=True)
                        extraction_loss = extraction_loss + default_loss
        
        # 语义表示（用于知识蒸馏）
        semantic_repr = self.semantic_projector(hidden_states.mean(dim=1))  # [batch_size, 256]
        
        # 🚀 双RTX4090激进训练：最终extraction_loss优化
        if not isinstance(extraction_loss, torch.Tensor):
            extraction_loss = torch.tensor(extraction_loss, device=local_hidden_states.device, requires_grad=True)

        # 最终数值稳定性检查
        if torch.isnan(extraction_loss).any() or torch.isinf(extraction_loss).any():
            logger.warning("⚠️ 最终extraction_loss包含NaN/Inf，使用合理默认值")
            extraction_loss = torch.tensor(5.0, device=local_hidden_states.device, requires_grad=True)
        else:
            # 合理的最终损失裁剪，允许充分学习
            extraction_loss = torch.clamp(extraction_loss, min=0.0, max=50.0)

        # DataParallel兼容性：最终检查确保所有field_predictions都是正确形状的tensor
        for field, pred in field_predictions.items():
            if not isinstance(pred, torch.Tensor):
                # 转换为tensor
                if isinstance(pred, list):
                    if pred and isinstance(pred[0], list):
                        # 二维列表，确保每个序列都是seq_len长度
                        padded_pred = []
                        for seq in pred:
                            if len(seq) < seq_len:
                                padded_seq = seq + [0] * (seq_len - len(seq))
                            elif len(seq) > seq_len:
                                padded_seq = seq[:seq_len]
                            else:
                                padded_seq = seq
                            padded_pred.append(padded_seq)
                        field_predictions[field] = torch.tensor(padded_pred, device=local_hidden_states.device, dtype=torch.long)
                    else:
                        # 一维列表，转换为二维
                        if len(pred) < seq_len:
                            pred = pred + [0] * (seq_len - len(pred))
                        elif len(pred) > seq_len:
                            pred = pred[:seq_len]
                        field_predictions[field] = torch.tensor([pred], device=local_hidden_states.device, dtype=torch.long)
                else:
                    # 标量，扩展为[batch_size, seq_len]
                    field_predictions[field] = torch.full((batch_size, seq_len), pred,
                                                         device=local_hidden_states.device, dtype=torch.long)
            else:
                # 已经是tensor，确保形状正确
                if pred.dim() == 1:
                    pred = pred.unsqueeze(0)  # 添加batch维度
                if pred.size(0) != batch_size:
                    # 调整batch维度
                    if pred.size(0) == 1:
                        pred = pred.expand(batch_size, -1)
                    else:
                        pred = pred[:batch_size]  # 截断
                if pred.size(1) != seq_len:
                    # 调整序列长度维度
                    if pred.size(1) < seq_len:
                        pad_size = seq_len - pred.size(1)
                        pad_tensor = torch.zeros(pred.size(0), pad_size,
                                               device=pred.device, dtype=pred.dtype)
                        pred = torch.cat([pred, pad_tensor], dim=1)
                    else:
                        pred = pred[:, :seq_len]
                field_predictions[field] = pred

        return {
            'field_logits': field_logits,
            'field_predictions': field_predictions,
            'extraction_loss': extraction_loss,
            'semantic_representation': semantic_repr,
            'logits': logits,  # 🚨 根本修复：添加logits用于知识蒸馏
            'last_hidden_state': hidden_states  # 保持兼容性
        }


class LegalExtractionModel(nn.Module):
    """司法结构化信息抽取模型"""
    
    def __init__(self, base_model: PreTrainedModel, config: ExtractionConfig):
        super().__init__()
        self.base_model = base_model
        self.config = config

        # 🔧 动态获取模型配置，适配Qwen系列
        if config.hidden_size is None:
            config.hidden_size = base_model.config.hidden_size
            logger.info(f"✅ 从base_model动态获取hidden_size: {config.hidden_size}")

        # 验证模型类型和配置
        model_type = getattr(base_model.config, 'model_type', 'unknown')
        vocab_size = getattr(base_model.config, 'vocab_size', 151936)
        logger.info(f"✅ 检测到模型类型: {model_type}")
        logger.info(f"✅ 模型配置: hidden_size={config.hidden_size}, vocab_size={vocab_size}")

        # 特殊处理Qwen模型
        if 'qwen' in model_type.lower():
            logger.info("🎯 检测到Qwen系列模型，应用专用配置")
            self._setup_qwen_specific_config()

        # 验证模型数据类型
        model_dtype = next(base_model.parameters()).dtype
        logger.info(f"✅ 模型数据类型: {model_dtype}")

        # 冻结base model的部分层（根据HPF策略）
        self._setup_parameter_freezing()

        # 添加抽取头
        self.extraction_head = LegalExtractionHead(config)

        # 🚨 根本架构修复：不需要手动创建lm_head
        # AutoModelForCausalLM已经包含了lm_head
        if hasattr(self.base_model, 'lm_head'):
            lm_head_shape = self.base_model.lm_head.weight.shape
            logger.info(f"✅ 使用base_model内置的lm_head: {lm_head_shape}")
        else:
            # 如果没有内置lm_head，创建一个备用的
            vocab_size = getattr(self.base_model.config, 'vocab_size', 151936)
            hidden_size = self.base_model.config.hidden_size
            self.lm_head = nn.Linear(hidden_size, vocab_size, bias=False)
            nn.init.normal_(self.lm_head.weight, mean=0.0, std=0.02)
            logger.info(f"⚠️ 创建备用lm_head: {hidden_size} -> {vocab_size}")

        logger.info("司法结构化信息抽取模型初始化完成")

    def _setup_qwen_specific_config(self):
        """设置Qwen模型特定配置"""
        try:
            # 获取Qwen模型的具体配置
            base_config = self.base_model.config

            # 检查并设置正确的vocab_size
            if hasattr(base_config, 'vocab_size'):
                vocab_size = base_config.vocab_size
                logger.info(f"✅ Qwen模型vocab_size: {vocab_size}")

            # 检查模型的数据类型
            if hasattr(self.base_model, 'dtype'):
                model_dtype = self.base_model.dtype
                logger.info(f"✅ Qwen模型数据类型: {model_dtype}")

            # 检查Qwen模型的层结构
            if hasattr(self.base_model, 'transformer'):
                if hasattr(self.base_model.transformer, 'h'):
                    layer_count = len(self.base_model.transformer.h)
                    logger.info(f"✅ Qwen模型层数 (transformer.h): {layer_count}")
                elif hasattr(self.base_model.transformer, 'layers'):
                    layer_count = len(self.base_model.transformer.layers)
                    logger.info(f"✅ Qwen模型层数 (transformer.layers): {layer_count}")
            elif hasattr(self.base_model, 'model') and hasattr(self.base_model.model, 'layers'):
                layer_count = len(self.base_model.model.layers)
                logger.info(f"✅ Qwen模型层数 (model.layers): {layer_count}")

            # 检查是否有lm_head
            if hasattr(self.base_model, 'lm_head'):
                lm_head_shape = self.base_model.lm_head.weight.shape
                logger.info(f"✅ Qwen模型lm_head形状: {lm_head_shape}")

        except Exception as e:
            logger.warning(f"Qwen特定配置设置失败: {e}")

    def gradient_checkpointing_enable(self, gradient_checkpointing_kwargs=None):
        """启用梯度检查点以节省内存"""
        if hasattr(self.base_model, 'gradient_checkpointing_enable'):
            self.base_model.gradient_checkpointing_enable(gradient_checkpointing_kwargs)

    def gradient_checkpointing_disable(self):
        """禁用梯度检查点"""
        if hasattr(self.base_model, 'gradient_checkpointing_disable'):
            self.base_model.gradient_checkpointing_disable()
    
    def _setup_parameter_freezing(self):
        """设置参数冻结策略"""
        # 这里会与HPF模块协调
        # 通常冻结底层的transformer层，只微调顶层和抽取头

        # 适配Qwen3模型结构 - 更准确的检测
        layers = None
        total_layers = 0

        # 方法1: 检查model.layers (Qwen3常用结构)
        if hasattr(self.base_model, 'model') and hasattr(self.base_model.model, 'layers'):
            layers = self.base_model.model.layers
            total_layers = len(layers)
            logger.info(f"✅ 检测到Qwen3结构: model.layers, 共{total_layers}层")

        # 方法2: 检查直接的layers属性
        elif hasattr(self.base_model, 'layers'):
            layers = self.base_model.layers
            total_layers = len(layers)
            logger.info(f"✅ 检测到layers结构, 共{total_layers}层")

        # 方法3: 检查transformer.h (其他模型)
        elif hasattr(self.base_model, 'transformer') and hasattr(self.base_model.transformer, 'h'):
            layers = self.base_model.transformer.h
            total_layers = len(layers)
            logger.info(f"✅ 检测到transformer.h结构, 共{total_layers}层")

        # 方法4: 检查transformer.layers
        elif hasattr(self.base_model, 'transformer') and hasattr(self.base_model.transformer, 'layers'):
            layers = self.base_model.transformer.layers
            total_layers = len(layers)
            logger.info(f"✅ 检测到transformer.layers结构, 共{total_layers}层")

        else:
            logger.warning("无法识别模型层结构，使用默认配置")
            logger.warning(f"模型属性: {[attr for attr in dir(self.base_model) if not attr.startswith('_')][:10]}")
            total_layers = 24  # 默认值
            layers = []

        # 冻结前70%的层
        freeze_layers = int(total_layers * 0.7)

        for i, layer in enumerate(layers):
            if i < freeze_layers:
                for param in layer.parameters():
                    param.requires_grad = False

        logger.info(f"冻结了前{freeze_layers}层，保持后{total_layers - freeze_layers}层可训练")
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None,
                extraction_labels: Dict[str, torch.Tensor] = None, **kwargs) -> Dict[str, Any]:
        """
        前向传播 - 修复版本

        Args:
            input_ids: [batch_size, seq_len]
            attention_mask: [batch_size, seq_len]
            extraction_labels: 抽取标签 {field_name: [batch_size, seq_len]}
        """
        # 🚨 关键修复：确保输入数据在正确设备上
        target_device = next(self.parameters()).device
        if input_ids.device != target_device:
            input_ids = input_ids.to(target_device)
        if attention_mask is not None and attention_mask.device != target_device:
            attention_mask = attention_mask.to(target_device)

        # 基本的输入检查（input_ids应该是整数，不会有NaN/Inf）
        if not torch.is_tensor(input_ids) or input_ids.dtype not in [torch.long, torch.int]:
            logger.error("input_ids类型错误")
            raise ValueError("input_ids必须是长整型tensor")

        # 🚨 关键修复：过滤kwargs，只传递安全的参数
        safe_kwargs = {}
        allowed_keys = {'use_cache', 'return_dict', 'output_attentions', 'output_hidden_states'}
        for key, value in kwargs.items():
            if key in allowed_keys:
                safe_kwargs[key] = value

        # 🚨 关键修复：强制禁用cache以避免梯度检查点冲突
        safe_kwargs['use_cache'] = False
        safe_kwargs['output_hidden_states'] = True  # 🚨 这是关键！必须获取hidden_states
        safe_kwargs['return_dict'] = True

        logger.debug(f"传递给base_model的参数: {safe_kwargs}")



        # Base model前向传播 - 使用AutoModel应该很稳定
        base_outputs = self.base_model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            **safe_kwargs
        )
        
        # 🚨 根本架构修复：正确获取hidden_states
        # 详细检查base_outputs的结构
        logger.debug(f"base_outputs类型: {type(base_outputs)}")
        logger.debug(f"base_outputs属性: {dir(base_outputs)}")

        hidden_states = None

        # 方法1：检查是否有last_hidden_state（AutoModel）
        if hasattr(base_outputs, 'last_hidden_state') and base_outputs.last_hidden_state is not None:
            hidden_states = base_outputs.last_hidden_state
            logger.debug("✅ 使用last_hidden_state")

        # 方法2：检查hidden_states tuple（AutoModelForCausalLM）
        elif hasattr(base_outputs, 'hidden_states') and base_outputs.hidden_states is not None:
            if isinstance(base_outputs.hidden_states, (list, tuple)) and len(base_outputs.hidden_states) > 0:
                hidden_states = base_outputs.hidden_states[-1]  # 最后一层
                logger.debug(f"✅ 使用hidden_states[-1]，共{len(base_outputs.hidden_states)}层")
            else:
                logger.error(f"🚨 hidden_states格式错误: {type(base_outputs.hidden_states)}")

        # 方法3：直接从transformer获取（备用方案）
        if hidden_states is None:
            logger.warning("🚨 尝试直接从transformer获取hidden_states")
            if hasattr(self.base_model, 'model'):  # Qwen的结构
                transformer_outputs = self.base_model.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    output_hidden_states=True,
                    return_dict=True
                )
                if hasattr(transformer_outputs, 'last_hidden_state'):
                    hidden_states = transformer_outputs.last_hidden_state
                    logger.debug("✅ 从base_model.model获取last_hidden_state")

        # 最后的错误处理
        if hidden_states is None:
            logger.error("🚨 所有方法都无法获取hidden_states！")
            logger.error(f"base_outputs: {base_outputs}")
            raise RuntimeError("无法从模型获取hidden_states，请检查模型结构")

        # 🚨 根本修复：检查hidden_states的数值有效性
        logger.debug(f"hidden_states形状: {hidden_states.shape}")
        logger.debug(f"hidden_states数据类型: {hidden_states.dtype}")
        logger.debug(f"hidden_states设备: {hidden_states.device}")

        # 🎯 关键修复：更智能的NaN/Inf处理
        has_nan = torch.isnan(hidden_states).any()
        has_inf = torch.isinf(hidden_states).any()

        if has_nan or has_inf:
            logger.warning("🚨 检测到数值不稳定性，尝试修复...")

            if has_nan:
                nan_count = torch.isnan(hidden_states).sum().item()
                logger.warning(f"发现{nan_count}个NaN值")

                # 🎯 智能修复：将NaN替换为0
                hidden_states = torch.where(torch.isnan(hidden_states),
                                          torch.zeros_like(hidden_states),
                                          hidden_states)
                logger.info("✅ NaN值已替换为0")

            if has_inf:
                inf_count = torch.isinf(hidden_states).sum().item()
                logger.warning(f"发现{inf_count}个Inf值")

                # 🎯 智能修复：将Inf替换为合理的最大值
                max_val = torch.finfo(hidden_states.dtype).max / 1000  # 避免溢出
                hidden_states = torch.where(torch.isinf(hidden_states),
                                          torch.sign(hidden_states) * max_val,
                                          hidden_states)
                logger.info("✅ Inf值已替换为合理范围")

            # 验证修复效果
            if torch.isnan(hidden_states).any() or torch.isinf(hidden_states).any():
                logger.error("🚨 数值修复失败，仍有异常值")
                raise RuntimeError("hidden_states数值修复失败")
            else:
                logger.info("✅ 数值稳定性修复成功")
        else:
            logger.debug("✅ hidden_states数值检查通过")

        # 确保设备一致性
        if hidden_states.device != target_device:
            hidden_states = hidden_states.to(target_device)



        # 🚨 根本架构修复：使用base_model的内置lm_head
        # 现在base_model是AutoModelForCausalLM，有内置的lm_head
        if hasattr(self.base_model, 'lm_head'):
            # 使用base_model的内置lm_head
            logits = self.base_model.lm_head(hidden_states)
        else:
            # 如果没有内置lm_head，使用我们自己创建的
            if not hasattr(self, 'lm_head') or self.lm_head is None:
                logger.error("🚨 没有可用的lm_head！")
                raise RuntimeError("需要lm_head进行知识蒸馏")

            # 确保设备和数据类型一致
            if self.lm_head.weight.device != target_device:
                self.lm_head = self.lm_head.to(target_device)
            if self.lm_head.weight.dtype != hidden_states.dtype:
                self.lm_head = self.lm_head.to(dtype=hidden_states.dtype)

            logits = self.lm_head(hidden_states)
        
        # 信息抽取
        extraction_outputs = self.extraction_head(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
            labels=extraction_labels
        )
        
        # 构建输出字典，确保DataParallel兼容性
        output_dict = {
            'extraction_outputs': extraction_outputs,
            'hidden_states': hidden_states,
            'logits': logits,  # 🚨 关键修复：始终包含logits用于知识蒸馏
        }

        # 如果base_outputs有logits，也包含进来（向后兼容）
        if hasattr(base_outputs, 'logits') and base_outputs.logits is not None:
            output_dict['base_logits'] = base_outputs.logits

        return output_dict
    
    def extract_information(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                          tokenizer: PreTrainedTokenizer) -> Dict[str, List[str]]:
        """
        执行信息抽取并返回结构化结果 - 🔧 修复碎片化问题

        Args:
            input_ids: 输入token ids
            attention_mask: 注意力掩码
            tokenizer: 分词器

        Returns:
            抽取的结构化信息 {field_name: [extracted_spans]}
        """
        self.eval()
        with torch.no_grad():
            outputs = self.forward(input_ids, attention_mask)
            predictions = outputs['extraction_outputs']['field_predictions']

            # 🔧 使用改进的span抽取方法，避免碎片化
            extracted_info = {}

            # 将input_ids转换为原始文本
            original_text = tokenizer.decode(input_ids[0], skip_special_tokens=True)

            for field, field_predictions in predictions.items():
                field_spans = []

                for batch_idx in range(input_ids.shape[0]):
                    spans = self._extract_meaningful_spans(
                        field_predictions[batch_idx] if isinstance(field_predictions, list)
                        else field_predictions[batch_idx].cpu().tolist(),
                        input_ids[batch_idx].cpu().tolist(),
                        tokenizer,
                        original_text,
                        field
                    )
                    field_spans.extend(spans)

                extracted_info[field] = field_spans

            return extracted_info
    
    def _extract_meaningful_spans(self, predictions: List[int], token_ids: List[int],
                                tokenizer: PreTrainedTokenizer, original_text: str,
                                field_name: str) -> List[str]:
        """
        🔧 改进的span抽取方法，避免碎片化问题

        Args:
            predictions: 模型预测结果
            token_ids: token ID列表
            tokenizer: 分词器
            original_text: 原始文本
            field_name: 字段名称

        Returns:
            有意义的文本片段列表
        """
        import re

        # 🔧 重新设计：精确的字段抽取模式
        field_patterns = {
            'case_facts': [
                r'经审理查明[：:]([^。]*?(?:被告人|案发|查明).*?)(?=本院认为|法院认为|上述事实)',
                r'公诉机关指控[：:]([^。]*?(?:被告人|指控|犯罪).*?)(?=经审理查明|本院认为)',
                r'检察院指控[：:]([^。]*?(?:被告人|指控|犯罪).*?)(?=经审理查明|本院认为)'
            ],
            'court_opinions': [
                r'本院认为[，：:]([^。]*?(?:构成|犯罪|处罚|从轻|减轻).*?)(?=综上|依照|判决如下|据此)',
                r'法院认为[，：:]([^。]*?(?:构成|犯罪|处罚|从轻|减轻).*?)(?=综上|依照|判决如下|据此)'
            ],
            'defendant_info': [
                r'被告人([^，。]*?)(?=[，。])',
                r'被告人([^，]*?)(?=因|以|伙同|单独|共同)',
                r'上诉人([^，。]*?)(?=[，。])'
            ],
            'judgment_results': [
                r'判处([^。]*?(?:有期徒刑|拘役|管制|罚金|死刑|无期徒刑)[^。]*?)(?=[。；])',
                r'以([^。]*?罪[^。]*?判处[^。]*?)(?=[。；])',
                r'构成([^。]*?罪[^。]*?)(?=[。；])',
                r'(有期徒刑[^。]*?)(?=[。；])',
                r'(罚金[^。]*?)(?=[。；])'
            ],
            'legal_reasoning': [
                r'依照(《[^》]*》第[^。]*?条[^。]*?)(?=[。；])',
                r'根据(《[^》]*》第[^。]*?条[^。]*?)(?=[。；])',
                r'按照(《[^》]*》第[^。]*?条[^。]*?)(?=[。；])',
                r'适用(《[^》]*》第[^。]*?条[^。]*?)(?=[。；])'
            ],
            'evidence_analysis': [
                r'(证据[^。]*?证实[^。]*?)(?=[。；])',
                r'(经查[^。]*?属实[^。]*?)(?=[。；])',
                r'(有[^。]*?证言[^。]*?)(?=[。；])',
                r'(经鉴定[^。]*?)(?=[。；])'
            ],
            'procedural_info': [
                r'(审理过程中[^。]*?)(?=[。；])',
                r'(开庭审理[^。]*?)(?=[。；])',
                r'(到案后[^。]*?)(?=[。；])',
                r'(归案后[^。]*?)(?=[。；])',
                r'(认罪认罚[^。]*?)(?=[。；])'
            ]
        }

        spans = []
        patterns = field_patterns.get(field_name, [])

        # 🔧 使用正则表达式提取有意义的文本段落
        for pattern in patterns:
            matches = re.finditer(pattern, original_text, re.DOTALL)
            for match in matches:
                span_text = match.group().strip()
                if len(span_text) > 10:  # 过滤过短的片段
                    spans.append(span_text)

        # 🔧 如果没有匹配到模式，使用改进的token-based方法
        if not spans:
            spans = self._fallback_token_extraction(predictions, token_ids, tokenizer)
            # 如果token方法也失败，使用关键词方法
            if not spans:
                spans = self._keyword_based_extraction(field_name, original_text)

        # 🔧 新增：结果后处理和质量过滤
        spans = self._post_process_spans(spans, field_name)

        return spans[:2]  # 限制每个字段最多2个片段，提高质量

    def _fallback_token_extraction(self, predictions: List[int], token_ids: List[int],
                                 tokenizer: PreTrainedTokenizer) -> List[str]:
        """备用的token级抽取方法"""
        spans = []
        current_span = []

        # 简化的连续token合并
        for i, (pred, token_id) in enumerate(zip(predictions, token_ids)):
            if pred > 0:  # 任何非0预测都认为是相关token
                current_span.append(token_id)
            else:
                if current_span and len(current_span) >= 3:  # 至少3个token才形成有意义的span
                    span_text = tokenizer.decode(current_span, skip_special_tokens=True)
                    if span_text.strip() and len(span_text.strip()) > 5:
                        spans.append(span_text.strip())
                current_span = []

        # 处理最后一个span
        if current_span and len(current_span) >= 3:
            span_text = tokenizer.decode(current_span, skip_special_tokens=True)
            if span_text.strip() and len(span_text.strip()) > 5:
                spans.append(span_text.strip())

        return spans

    def _keyword_based_extraction(self, field_name: str, text: str) -> List[str]:
        """基于关键词的备用抽取方法"""
        import re
        spans = []

        # 字段特定的关键词和模式
        field_keywords = {
            'case_facts': ['案件事实', '经审理查明', '公诉机关指控', '检察院指控'],
            'court_opinions': ['本院认为', '法院认为', '经审理认为', '审理认为'],
            'defendant_info': ['被告人', '上诉人', '申请人'],
            'judgment_results': ['判决如下', '裁定如下', '判处', '构成', '罪'],
            'legal_reasoning': ['依照', '根据', '按照', '适用', '第', '条'],
            'evidence_analysis': ['证据', '证实', '经查', '属实', '鉴定'],
            'procedural_info': ['受理', '审理过程', '开庭', '到案', '归案']
        }

        keywords = field_keywords.get(field_name, [])

        # 按句子分割文本
        sentences = re.split(r'[。！？；]', text)

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # 过滤过短的句子
                # 检查是否包含相关关键词
                for keyword in keywords:
                    if keyword in sentence:
                        spans.append(sentence)
                        break

        return spans[:3]  # 限制数量

    def _post_process_spans(self, spans: List[str], field_name: str) -> List[str]:
        """后处理抽取结果，提高质量"""
        if not spans:
            return spans

        processed_spans = []

        for span in spans:
            span = span.strip()

            # 基本质量过滤
            if len(span) < 5:  # 过短
                continue
            if len(span) > 500:  # 过长，截断
                span = span[:500] + "..."

            # 字段特定的后处理
            if field_name == 'defendant_info':
                # 被告人信息：确保包含姓名
                if not any(keyword in span for keyword in ['被告人', '上诉人', '申请人']):
                    continue
                # 移除过长的描述，只保留关键信息
                if len(span) > 100:
                    span = span[:100]

            elif field_name == 'judgment_results':
                # 判决结果：确保包含刑罚信息
                if not any(keyword in span for keyword in ['有期徒刑', '拘役', '管制', '罚金', '死刑', '无期徒刑', '构成', '判处']):
                    continue

            elif field_name == 'legal_reasoning':
                # 法律推理：确保包含法条
                if not any(keyword in span for keyword in ['第', '条', '法', '依照', '根据']):
                    continue

            elif field_name == 'court_opinions':
                # 法院观点：确保包含法院判断
                if not any(keyword in span for keyword in ['认为', '构成', '犯罪', '处罚']):
                    continue

            elif field_name == 'evidence_analysis':
                # 证据分析：确保包含证据相关词汇
                if not any(keyword in span for keyword in ['证据', '证实', '证明', '查明', '鉴定']):
                    continue

            elif field_name == 'procedural_info':
                # 程序信息：确保包含程序相关词汇
                if not any(keyword in span for keyword in ['审理', '开庭', '到案', '归案', '认罪']):
                    continue

            # 清理格式
            span = self._clean_span_format(span)

            if span and span not in processed_spans:  # 去重
                processed_spans.append(span)

        return processed_spans

    def _clean_span_format(self, span: str) -> str:
        """清理抽取结果的格式"""
        # 移除多余的空白字符
        span = ' '.join(span.split())

        # 移除开头的标点符号
        while span and span[0] in '，。；：':
            span = span[1:]

        # 确保以句号结尾（如果不是以其他标点结尾）
        if span and span[-1] not in '。！？；':
            span += '。'

        return span


# 导出的主要接口
__all__ = [
    'ExtractionConfig',
    'LegalExtractionHead', 
    'LegalExtractionModel'
]
