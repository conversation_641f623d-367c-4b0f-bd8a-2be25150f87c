"""
司法结构化信息抽取数据加载器

专门为结构化信息抽取任务设计，不进行分类任务
重点：从司法文书中抽取结构化信息，提高抽取准确性
"""

import os
import json
import torch
import logging
import re
from typing import Dict, List, Optional, Tuple, Any, Union
from torch.utils.data import Dataset
from transformers import PreTrainedTokenizer
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ExtractionSample:
    """信息抽取样本"""
    text: str                           # 原始文本
    extracted_fields: Dict[str, List[str]]  # 抽取的字段 {field_name: [spans]}
    field_labels: Dict[str, List[int]]      # BIO标注 {field_name: [labels]}
    metadata: Dict[str, Any]                # 元数据


class LegalExtractionDataset(Dataset):
    """司法结构化信息抽取数据集"""
    
    def __init__(self,
                 data_file: str,
                 tokenizer: PreTrainedTokenizer,
                 config: Dict[str, Any] = None,
                 max_length: int = 1024,
                 is_training: bool = True,
                 extraction_fields: List[str] = None,
                 innovation_manager=None):  # 新增：UDP预处理支持
        """
        初始化信息抽取数据集

        Args:
            data_file: 数据文件路径
            tokenizer: 分词器
            config: 配置字典
            max_length: 最大序列长度
            is_training: 是否为训练模式
            extraction_fields: 要抽取的字段列表
            innovation_manager: 创新模块管理器（用于UDP预处理）
        """
        self.data_file = data_file
        self.tokenizer = tokenizer
        self.config = config or {}
        self.max_length = max_length
        self.is_training = is_training
        self.innovation_manager = innovation_manager  # 新增
        
        # 设置抽取字段 - 与实际数据字段对应
        self.extraction_fields = extraction_fields or [
            'fact',                 # 案件事实
            'court_view',           # 法院观点
            'defendants',           # 被告信息
            'outcomes',             # 判决结果
            'relevant_articles'     # 相关法条
        ]
        
        # UDP预处理配置
        self.enable_udp_preprocessing = innovation_manager is not None
        if self.enable_udp_preprocessing:
            logger.info("✅ 启用UDP数据预处理")
        else:
            logger.info("⚠️ 未启用UDP数据预处理")

        # 加载数据
        self.samples = self._load_and_process_data()

        logger.info(f"加载司法信息抽取数据集: {len(self.samples)} 个样本")
        logger.info(f"抽取字段: {self.extraction_fields}")
        logger.info(f"最大长度: {max_length}, 训练模式: {is_training}")
    
    def _load_and_process_data(self) -> List[ExtractionSample]:
        """加载和处理数据"""
        samples = []
        
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())
                        sample = self._process_single_sample(data, line_num)
                        if sample:
                            samples.append(sample)
                    except json.JSONDecodeError as e:
                        logger.warning(f"第{line_num}行JSON解析失败: {e}")
                    except Exception as e:
                        logger.warning(f"第{line_num}行处理失败: {e}")
        
        except FileNotFoundError:
            logger.error(f"数据文件不存在: {self.data_file}")
            raise
        
        return samples
    
    def _process_single_sample(self, data: Dict[str, Any], line_num: int) -> Optional[ExtractionSample]:
        """处理单个样本"""
        try:
            # 构建输入文本
            text = self._build_input_text(data)
            
            # 抽取结构化信息
            extracted_fields = self._extract_structured_info(data)
            
            # 生成BIO标注（如果是训练模式）
            field_labels = {}
            if self.is_training:
                field_labels = self._generate_bio_labels(text, extracted_fields)
            
            # 元数据
            metadata = {
                'line_num': line_num,
                'original_data': data,
                'text_length': len(text)
            }
            
            return ExtractionSample(
                text=text,
                extracted_fields=extracted_fields,
                field_labels=field_labels,
                metadata=metadata
            )
            
        except Exception as e:
            logger.warning(f"样本处理失败 (第{line_num}行): {e}")
            return None
    
    def _build_input_text(self, data: Dict[str, Any]) -> str:
        """构建输入文本"""
        # 从司法文书数据中构建完整的输入文本
        text_parts = []
        
        # 案件事实
        if 'fact' in data and data['fact']:
            text_parts.append(f"案件事实：{data['fact']}")
        
        # 法院观点
        if 'court_view' in data and data['court_view']:
            text_parts.append(f"法院观点：{data['court_view']}")
        
        # 被告信息
        if 'defendants' in data and data['defendants']:
            defendants_text = self._format_defendants(data['defendants'])
            if defendants_text:
                text_parts.append(f"被告信息：{defendants_text}")
        
        # 判决结果
        if 'outcomes' in data and data['outcomes']:
            outcomes_text = self._format_outcomes(data['outcomes'])
            if outcomes_text:
                text_parts.append(f"判决结果：{outcomes_text}")
        
        return "\n".join(text_parts)
    
    def _format_defendants(self, defendants: List[Dict]) -> str:
        """格式化被告信息"""
        defendant_texts = []
        for defendant in defendants:
            parts = []
            if 'name' in defendant:
                parts.append(f"姓名：{defendant['name']}")
            if 'gender' in defendant:
                parts.append(f"性别：{defendant['gender']}")
            if 'age' in defendant:
                parts.append(f"年龄：{defendant['age']}")
            if 'ethnicity' in defendant:
                parts.append(f"民族：{defendant['ethnicity']}")
            
            if parts:
                defendant_texts.append("，".join(parts))
        
        return "；".join(defendant_texts)
    
    def _format_outcomes(self, outcomes: List[Dict]) -> str:
        """格式化判决结果"""
        outcome_texts = []
        for outcome in outcomes:
            if 'judgment' in outcome:
                for judgment in outcome['judgment']:
                    parts = []
                    if 'accusation' in judgment:
                        parts.append(f"罪名：{judgment['accusation']}")
                    if 'imprisonment' in judgment:
                        parts.append(f"刑期：{judgment['imprisonment']}")
                    if 'fine' in judgment:
                        parts.append(f"罚金：{judgment['fine']}")
                    
                    if parts:
                        outcome_texts.append("，".join(parts))
        
        return "；".join(outcome_texts)
    
    def _extract_structured_info(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """从原始数据中抽取结构化信息 - 使用实际数据字段"""
        extracted = {}

        # 案件事实 - 直接使用fact字段
        extracted['fact'] = [data.get('fact', '')] if data.get('fact') else []

        # 法院观点 - 直接使用court_view字段
        extracted['court_view'] = [data.get('court_view', '')] if data.get('court_view') else []

        # 被告信息 - 处理defendants字段
        defendant_info = []
        if 'defendants' in data:
            if isinstance(data['defendants'], list):
                # 如果是字符串列表（简单格式）
                for defendant in data['defendants']:
                    if isinstance(defendant, str):
                        defendant_info.append(f"被告人：{defendant}")
                    elif isinstance(defendant, dict):
                        # 如果是字典格式（复杂格式）
                        info_parts = []
                        for key in ['name', 'gender', 'age', 'ethnicity']:
                            if key in defendant and defendant[key]:
                                info_parts.append(f"{key}:{defendant[key]}")
                        if info_parts:
                            defendant_info.append(",".join(info_parts))
        extracted['defendants'] = defendant_info

        # 判决结果 - 直接使用outcomes字段
        judgment_results = []
        if 'outcomes' in data:
            for outcome in data['outcomes']:
                if 'judgment' in outcome:
                    for judgment in outcome['judgment']:
                        result_parts = []
                        # 罪名
                        if 'accusation' in judgment and judgment['accusation']:
                            result_parts.append(f"罪名：{judgment['accusation']}")
                        # 刑期处理
                        if 'penalty' in judgment and judgment['penalty']:
                            penalty = judgment['penalty']
                            if penalty.get('imprisonment', 0) > 0:
                                months = penalty['imprisonment']
                                if months >= 12:
                                    years = months // 12
                                    remaining_months = months % 12
                                    if remaining_months > 0:
                                        result_parts.append(f"刑期：有期徒刑{years}年{remaining_months}个月")
                                    else:
                                        result_parts.append(f"刑期：有期徒刑{years}年")
                                else:
                                    result_parts.append(f"刑期：有期徒刑{months}个月")
                            elif penalty.get('detention', 0) > 0:
                                result_parts.append(f"刑期：拘役{penalty['detention']}个月")
                            elif penalty.get('surveillance', 0) > 0:
                                result_parts.append(f"刑期：管制{penalty['surveillance']}个月")
                            # 罚金
                            if penalty.get('fine', 0) > 0:
                                result_parts.append(f"罚金：{penalty['fine']}元")
                            elif penalty.get('fine_without_amount', False):
                                result_parts.append("罚金：数额另定")

                        if result_parts:
                            judgment_results.append("，".join(result_parts))
        extracted['outcomes'] = judgment_results

        # 相关法条 - 直接使用relevant_articles字段
        relevant_articles = []
        if 'relevant_articles' in data and data['relevant_articles']:
            if isinstance(data['relevant_articles'], list):
                for article in data['relevant_articles']:
                    if isinstance(article, str):
                        relevant_articles.append(f"法条：{article}")
                    else:
                        relevant_articles.append(f"法条：{str(article)}")
        extracted['relevant_articles'] = relevant_articles

        return extracted
    
    def _generate_bio_labels(self, text: str, extracted_fields: Dict[str, List[str]]) -> Dict[str, List[int]]:
        """生成BIO标注 - 修复版本"""
        # 使用tokenizer进行编码以获得准确的token
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding=False,
            truncation=True,
            return_offsets_mapping=True,
            return_tensors=None
        )

        tokens = encoding['input_ids']
        offset_mapping = encoding.get('offset_mapping', [])
        seq_len = len(tokens)

        field_labels = {}
        for field in self.extraction_fields:
            # 初始化为O标签（0）
            labels = [0] * seq_len  # 0=O, 1=B, 2=I

            # 修复：改进的BIO标注生成
            field_texts = extracted_fields.get(field, [])
            if field_texts and offset_mapping:
                for field_text in field_texts:
                    if field_text and len(field_text.strip()) > 3:
                        field_text_clean = field_text.strip()

                        # 在原文中查找字段文本的位置
                        start_char = text.find(field_text_clean)
                        if start_char != -1:
                            end_char = start_char + len(field_text_clean)

                            # 找到对应的token范围
                            start_token = -1
                            end_token = -1

                            for i, (token_start, token_end) in enumerate(offset_mapping):
                                if token_start >= start_char and start_token == -1:
                                    start_token = i
                                if token_end <= end_char:
                                    end_token = i
                                elif start_token != -1:
                                    break

                            # 设置BIO标签
                            if start_token != -1 and end_token != -1:
                                if start_token < seq_len:
                                    labels[start_token] = 1  # B标签
                                for i in range(start_token + 1, min(end_token + 1, seq_len)):
                                    labels[i] = 2  # I标签

            field_labels[field] = labels

        return field_labels

    def _build_char_to_token_mapping(self, text: str, tokens: List[str]) -> Dict[int, int]:
        """构建字符位置到token位置的映射"""
        char_to_token = {}
        current_pos = 0

        for token_idx, token in enumerate(tokens):
            # 处理特殊token
            if token.startswith('##'):
                token_text = token[2:]
            elif token in ['[CLS]', '[SEP]', '[PAD]', '[UNK]']:
                continue
            else:
                token_text = token

            # 在文本中查找token
            while current_pos < len(text):
                if text[current_pos:current_pos + len(token_text)] == token_text:
                    # 记录这个token对应的字符位置
                    for i in range(len(token_text)):
                        if current_pos + i < len(text):
                            char_to_token[current_pos + i] = token_idx
                    current_pos += len(token_text)
                    break
                else:
                    current_pos += 1

        return char_to_token
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """获取单个样本 - 集成UDP预处理"""
        sample = self.samples[idx]

        # 🚀 UDP预处理 - 在数据加载阶段进行（修复：传入tokenizer启用所有模块）
        processed_text = sample.text
        enhanced_input_ids = None
        enhanced_attention_mask = None

        if self.enable_udp_preprocessing:
            try:
                # 🔧 修复：传入tokenizer以启用分区提取和协作标注
                udp_result = self.innovation_manager.apply_udp_processing(
                    text=sample.text,
                    tokenizer=self.tokenizer  # 传入tokenizer启用阶段2
                )

                if udp_result and 'enhanced_text' in udp_result:
                    processed_text = udp_result['enhanced_text']
                    # 如果UDP返回了增强的token序列，优先使用
                    if 'enhanced_input_ids' in udp_result and udp_result['enhanced_input_ids'] is not None:
                        enhanced_input_ids = udp_result['enhanced_input_ids']
                    if 'enhanced_attention_mask' in udp_result and udp_result['enhanced_attention_mask'] is not None:
                        enhanced_attention_mask = udp_result['enhanced_attention_mask']

                    logger.debug(f"✅ 样本{idx} UDP完整预处理完成 (LSE+分区提取)")
                else:
                    logger.debug(f"⚠️ 样本{idx} UDP预处理返回空结果")

            except Exception as e:
                logger.warning(f"样本{idx} UDP预处理失败: {e}")
                # 继续使用原始文本

        # 分词和编码（优先使用UDP增强的token序列）
        if enhanced_input_ids is not None and enhanced_attention_mask is not None:
            # 使用UDP预处理的token序列，确保正确的长度和形状
            input_ids = enhanced_input_ids.squeeze(0) if enhanced_input_ids.dim() > 1 else enhanced_input_ids
            attention_mask = enhanced_attention_mask.squeeze(0) if enhanced_attention_mask.dim() > 1 else enhanced_attention_mask

            # 🔧 修复：确保UDP增强的序列也符合max_length要求
            if len(input_ids) > self.max_length:
                input_ids = input_ids[:self.max_length]
                attention_mask = attention_mask[:self.max_length]
            elif len(input_ids) < self.max_length:
                # 填充到max_length
                pad_length = self.max_length - len(input_ids)
                input_ids = torch.cat([input_ids, torch.zeros(pad_length, dtype=input_ids.dtype)])
                attention_mask = torch.cat([attention_mask, torch.zeros(pad_length, dtype=attention_mask.dtype)])

            encoding = {
                'input_ids': input_ids,
                'attention_mask': attention_mask
            }
            logger.debug(f"✅ 样本{idx} 使用UDP增强的token序列，长度: {len(input_ids)}")
        else:
            # 标准分词编码（使用预处理后的文本）
            encoding = self.tokenizer(
                processed_text,
                max_length=self.max_length,
                padding='max_length',
                truncation=True,
                return_tensors='pt'
            )
            # 移除batch维度
            encoding = {k: v.squeeze(0) for k, v in encoding.items()}
            logger.debug(f"✅ 样本{idx} 使用标准分词，长度: {len(encoding['input_ids'])}")

        # 🔧 修复：确保所有张量都是正确的形状和长度
        input_ids = encoding['input_ids']
        attention_mask = encoding['attention_mask']

        # 最终长度检查和修正
        if len(input_ids) != self.max_length:
            logger.warning(f"样本{idx} input_ids长度不匹配: {len(input_ids)} != {self.max_length}")
            if len(input_ids) > self.max_length:
                input_ids = input_ids[:self.max_length]
                attention_mask = attention_mask[:self.max_length]
            else:
                pad_length = self.max_length - len(input_ids)
                input_ids = torch.cat([input_ids, torch.zeros(pad_length, dtype=input_ids.dtype)])
                attention_mask = torch.cat([attention_mask, torch.zeros(pad_length, dtype=attention_mask.dtype)])

        result = {
            'input_ids': input_ids,  # 确保是1D张量，长度为max_length
            'attention_mask': attention_mask,  # 确保是1D张量，长度为max_length
            'text': sample.text,  # 保留原始文本用于对比
            'processed_text': processed_text,  # 新增：预处理后的文本
            'extracted_fields': sample.extracted_fields,
            'metadata': sample.metadata
        }
        
        # 添加标签（训练时）
        if self.is_training and sample.field_labels:
            for field, labels in sample.field_labels.items():
                # 确保标签长度与input_ids一致
                # 对于CRF层，使用0（O标签）而不是-100进行填充
                padded_labels = labels + [0] * (self.max_length - len(labels))
                padded_labels = padded_labels[:self.max_length]
                result[f'{field}_labels'] = torch.tensor(padded_labels, dtype=torch.long)
        
        return result


def extraction_collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """信息抽取任务的批处理函数 - 修复张量形状不一致问题"""

    # 🔧 修复：添加张量形状检查和调试信息
    logger = logging.getLogger(__name__)

    # 检查所有input_ids的形状
    input_ids_shapes = [item['input_ids'].shape for item in batch]
    attention_mask_shapes = [item['attention_mask'].shape for item in batch]

    logger.debug(f"批次大小: {len(batch)}")
    logger.debug(f"input_ids形状: {input_ids_shapes}")
    logger.debug(f"attention_mask形状: {attention_mask_shapes}")

    # 检查形状一致性
    if not all(shape == input_ids_shapes[0] for shape in input_ids_shapes):
        logger.error(f"🚨 input_ids形状不一致: {input_ids_shapes}")
        # 尝试修复：找到最大长度并填充
        max_length = max(shape[0] if len(shape) > 0 else 0 for shape in input_ids_shapes)
        logger.warning(f"尝试修复：统一长度为 {max_length}")

        fixed_input_ids = []
        fixed_attention_mask = []

        for i, item in enumerate(batch):
            input_ids = item['input_ids']
            attention_mask = item['attention_mask']

            if len(input_ids) < max_length:
                # 填充
                pad_length = max_length - len(input_ids)
                input_ids = torch.cat([input_ids, torch.zeros(pad_length, dtype=input_ids.dtype)])
                attention_mask = torch.cat([attention_mask, torch.zeros(pad_length, dtype=attention_mask.dtype)])
            elif len(input_ids) > max_length:
                # 截断
                input_ids = input_ids[:max_length]
                attention_mask = attention_mask[:max_length]

            fixed_input_ids.append(input_ids)
            fixed_attention_mask.append(attention_mask)

        input_ids = torch.stack(fixed_input_ids)
        attention_mask = torch.stack(fixed_attention_mask)
    else:
        # 正常情况：所有张量形状一致
        input_ids = torch.stack([item['input_ids'] for item in batch])
        attention_mask = torch.stack([item['attention_mask'] for item in batch])
    
    result = {
        'input_ids': input_ids,
        'attention_mask': attention_mask,
        'texts': [item['text'] for item in batch],
        'extracted_fields': [item['extracted_fields'] for item in batch],
        'metadata': [item['metadata'] for item in batch]
    }
    
    # 标签字段（如果存在）- 使用实际数据字段
    extraction_fields = [
        'fact', 'court_view', 'defendants', 'outcomes', 'relevant_articles'
    ]

    extraction_labels = {}
    for field in extraction_fields:
        field_key = f'{field}_labels'
        if field_key in batch[0]:
            extraction_labels[field] = torch.stack([item[field_key] for item in batch])

    if extraction_labels:
        result['extraction_labels'] = extraction_labels
    
    return result


# 导出的主要接口
__all__ = [
    'ExtractionSample',
    'LegalExtractionDataset',
    'extraction_collate_fn'
]
