2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 100 | ⚡ 生产模式已启用 - DEBUG日志已关闭以加速训练
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 103 | ================================================================================
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 104 | 🚀 司法信息抽取训练开始
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 105 | ================================================================================
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 106 | 📅 训练开始时间: 2025-08-10 14:10:39
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 107 | 📁 主日志文件: ./logs\extraction_training_20250810_141039.log
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 108 | 📁 错误日志文件: ./logs\extraction_errors_20250810_141039.log
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 109 | 🖥️  Python版本: 3.11.5 | packaged by Anaconda, Inc. | (main, Sep 11 2023, 13:26:23) [MSC v.1916 64 bit (AMD64)]
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 110 | 🔥 PyTorch版本: 2.6.0+cpu
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 111 | 🎯 CUDA可用: False
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 121 | 💾 系统内存: 13.7GB (可用: 2.0GB)
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 122 | 🔧 CPU核心数: 16
2025-08-10 14:10:39 |     INFO | unified_extraction_train |   setup_logging: 137 | ✅ 异常钩子已设置，将捕获所有未处理的异常
2025-08-10 14:10:39 |     INFO | models.gpu_memory_optimizer | _initialize_device_info:  70 | === GPU设备信息 ===
2025-08-10 14:10:39 |     INFO | models.gpu_memory_optimizer | _initialize_device_info:  92 | CPU内存: 13.7GB
2025-08-10 14:10:39 |     INFO | models.gpu_memory_optimizer | _initialize_device_info:  93 | ==================
2025-08-10 14:10:39 |     INFO | models.gpu_memory_optimizer |        __init__:  64 | GPU内存优化器初始化完成
2025-08-10 14:10:39 |     INFO | models.unified_innovation_manager |        <module>:  27 | ✅ 所有创新模块导入成功
2025-08-10 14:10:39 |     INFO | unified_extraction_train |        <module>: 339 | ✅ 所有信息抽取模块导入成功
2025-08-10 14:10:39 |     INFO | data.unified_data_config |        validate:  90 | 验证统一数据配置...
2025-08-10 14:10:39 |     INFO | data.unified_data_config |        validate: 113 | ✅ 训练文件: .\cloud_data\train_small.jsonl
2025-08-10 14:10:39 |     INFO | data.unified_data_config |        validate: 113 | ✅ 验证文件: .\cloud_data\valid_small.jsonl
2025-08-10 14:10:39 |     INFO | data.unified_data_config |        validate: 113 | ✅ 测试文件: .\cloud_data\test_small.jsonl
2025-08-10 14:10:39 |     INFO | data.unified_data_config |        validate: 117 | ✅ 统一数据配置验证通过
2025-08-10 14:10:39 |     INFO | data.unified_data_config | create_unified_data_config: 299 | ✅ 统一数据配置创建成功
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 0
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 1
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 2
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 3
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 4
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 5
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 6
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 7
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 8
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 9
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 10
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 11
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 12
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 13
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 14
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 15
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 16
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 17
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 18
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.18.weight (Layer 18)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.18.bias (Layer 18)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 19
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.19.weight (Layer 19)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.19.bias (Layer 19)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 20
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.20.weight (Layer 20)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.20.bias (Layer 20)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 21
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.21.weight (Layer 21)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.21.bias (Layer 21)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 22
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.22.weight (Layer 22)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.22.bias (Layer 22)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 23
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.23.weight (Layer 23)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.23.bias (Layer 23)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 24
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.24.weight (Layer 24)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.24.bias (Layer 24)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 25
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.25.weight (Layer 25)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.25.bias (Layer 25)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 26
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.26.weight (Layer 26)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.26.bias (Layer 26)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 27
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.27.weight (Layer 27)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.27.bias (Layer 27)
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 366 | HPF参数分组结果:
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   embedding: 0 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   encoder: 56 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   decoder: 0 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   output: 0 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   entity_layers: 8 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   relation_layers: 8 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   reasoning_layers: 4 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _apply_hierarchical_freezing: 461 | 开始应用层次化参数冻结策略...
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 0
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 1
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 2
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 3
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 4
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 5
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 6
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 7
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 8
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 9
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 10
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 11
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 12
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 13
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 14
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 15
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 16
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 17
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 18
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.18.weight (Layer 18)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.18.bias (Layer 18)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 19
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.19.weight (Layer 19)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.19.bias (Layer 19)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 20
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.20.weight (Layer 20)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.20.bias (Layer 20)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 21
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.21.weight (Layer 21)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 353 | 实体层参数: model.layers.21.bias (Layer 21)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 22
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.22.weight (Layer 22)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.22.bias (Layer 22)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 23
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.23.weight (Layer 23)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.23.bias (Layer 23)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 24
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.24.weight (Layer 24)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.24.bias (Layer 24)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 25
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.25.weight (Layer 25)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 356 | 关系层参数: model.layers.25.bias (Layer 25)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 26
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.26.weight (Layer 26)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.26.bias (Layer 26)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _extract_layer_index: 395 | 发现层索引: Layer 27
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.27.weight (Layer 27)
2025-08-10 14:10:39 |    DEBUG | src.models.hpf_module | _create_parameter_groups: 359 | 推理层参数: model.layers.27.bias (Layer 27)
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 366 | HPF参数分组结果:
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   embedding: 0 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   encoder: 56 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   decoder: 0 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   output: 0 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   entity_layers: 8 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   relation_layers: 8 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _create_parameter_groups: 368 |   reasoning_layers: 4 个参数
2025-08-10 14:10:39 |     INFO | src.models.hpf_module | _apply_hierarchical_freezing: 461 | 开始应用层次化参数冻结策略...
