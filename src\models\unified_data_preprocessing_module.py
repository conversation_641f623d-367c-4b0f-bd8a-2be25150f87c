"""
统一数据预处理模块 (Unified Data Preprocessing Module, UDP)

这是本研究的第二个核心创新技术，将原来的三个数据预处理模块合并：
1. LSE (Legal Semantic Enhancement) - 法律语义增强
2. Collaborative Annotation - 协作标注
3. Partition Extraction - 分区提取

主要特点：
1. 统一的数据预处理流水线
2. 法律语义增强和实体识别
3. 多视角协作标注
4. 智能文档分区处理
5. 端到端的数据优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoTokenizer
from typing import Dict, List, Optional, Tuple, Any, Set
import logging
import re
import numpy as np
from dataclasses import dataclass
from collections import defaultdict

# jieba作为可选依赖
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    jieba = None

logger = logging.getLogger(__name__)


@dataclass
class UDPConfig:
    """统一数据预处理配置类"""
    # 基础配置
    hidden_size: int = 2048  # 适配Qwen3-1.7B
    max_sequence_length: int = 4096
    dropout: float = 0.1
    
    # LSE相关配置
    enable_legal_semantic_enhancement: bool = True
    entity_recognition_enabled: bool = True
    relation_extraction_enabled: bool = True
    knowledge_graph_enabled: bool = True
    kg_embedding_dim: int = 2048
    entity_types: List[str] = None
    relation_types: List[str] = None
    
    # 协作标注配置
    enable_collaborative_annotation: bool = True
    num_annotators: int = 3
    confidence_threshold: float = 0.8
    enable_dynamic_weighting: bool = True
    consistency_weight: float = 0.3
    diversity_weight: float = 0.2
    
    # 分区提取配置
    enable_partition_extraction: bool = True
    max_partition_length: int = 512
    num_partitions: int = 4
    partition_strategy: str = "semantic"  # semantic, length_based, hybrid
    overlap_ratio: float = 0.1
    min_partition_length: int = 50
    enable_smart_splitting: bool = True
    aggregation_method: str = "weighted_average"
    
    # 特征融合配置
    feature_fusion_method: str = "attention"  # attention, concat, gate
    fusion_layers: List[int] = None
    enhancement_strength: float = 0.1
    
    def __post_init__(self):
        if self.entity_types is None:
            self.entity_types = ["PERSON", "ORG", "LAW", "CRIME", "PENALTY", "DATE", "MONEY"]
        if self.relation_types is None:
            self.relation_types = ["COMMIT", "JUDGE", "PENALTY", "APPEAL", "EVIDENCE"]
        if self.fusion_layers is None:
            self.fusion_layers = [14, 21, 27]  # 适配28层Qwen3-1.7B模型


class LegalEntityRecognizer:
    """法律实体识别器 - 从LSE模块迁移"""
    
    def __init__(self, config: UDPConfig):
        self.config = config
        self.entity_patterns = self._build_entity_patterns()
        
    def _build_entity_patterns(self) -> Dict[str, List[str]]:
        """构建实体识别模式"""
        patterns = {
            "PERSON": [
                r"被告人?[^\s，。；！？]{1,4}",
                r"原告[^\s，。；！？]{1,4}",
                r"证人[^\s，。；！？]{1,4}",
                r"[^\s，。；！？]{1,4}(?=被告|原告|证人)"
            ],
            "ORG": [
                r"[^\s，。；！？]*法院",
                r"[^\s，。；！？]*检察院",
                r"[^\s，。；！？]*公安局",
                r"[^\s，。；！？]*律师事务所"
            ],
            "LAW": [
                r"《[^》]*法》",
                r"《[^》]*条例》",
                r"《[^》]*规定》",
                r"第[一二三四五六七八九十百千万\d]+条"
            ],
            "CRIME": [
                r"[^\s，。；！？]*罪",
                r"犯[^\s，。；！？]*罪"
            ],
            "PENALTY": [
                r"有期徒刑[^\s，。；！？]*",
                r"拘役[^\s，。；！？]*",
                r"管制[^\s，。；！？]*",
                r"罚金[^\s，。；！？]*",
                r"死刑",
                r"无期徒刑"
            ],
            "DATE": [
                r"\d{4}年\d{1,2}月\d{1,2}日",
                r"\d{4}年\d{1,2}月",
                r"\d{1,2}月\d{1,2}日"
            ],
            "MONEY": [
                r"\d+(?:\.\d+)?万?元",
                r"人民币\d+(?:\.\d+)?万?元"
            ]
        }
        return patterns
    
    def extract_entities(self, text: str) -> Dict[str, List[Dict[str, Any]]]:
        """提取文本中的法律实体"""
        entities = defaultdict(list)
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    entity = {
                        "text": match.group(),
                        "start": match.start(),
                        "end": match.end(),
                        "type": entity_type
                    }
                    entities[entity_type].append(entity)
        
        return dict(entities)


class LegalTextSplitter:
    """司法文本智能分割器 - 从分区提取模块迁移"""
    
    def __init__(self, config: UDPConfig):
        self.config = config
        
        # 司法文档结构模式
        self.section_patterns = {
            'parties': [
                r'当事人.*?：', r'被告人.*?：', r'原告.*?：',
                r'第三人.*?：', r'上诉人.*?：', r'被上诉人.*?：'
            ],
            'fact': [
                r'经审理查明.*?：', r'查明.*?：', r'事实.*?：',
                r'案件事实.*?：', r'基本事实.*?：'
            ],
            'judgment': [
                r'本院认为.*?：', r'法院认为.*?：', r'判决如下.*?：',
                r'裁定如下.*?：', r'审理认为.*?：'
            ],
            'legal_basis': [
                r'依据.*?法.*?条', r'根据.*?法.*?条',
                r'按照.*?法.*?条', r'《.*?法》.*?条'
            ]
        }
    
    def split_document(self, text: str, tokenizer) -> List[Dict[str, Any]]:
        """智能分割司法文档"""
        partitions = []
        
        if self.config.enable_smart_splitting:
            partitions = self._smart_split(text, tokenizer)
        else:
            partitions = self._simple_split(text, tokenizer)
        
        return partitions
    
    def _smart_split(self, text: str, tokenizer) -> List[Dict[str, Any]]:
        """基于司法文档结构的智能分割"""
        partitions = []
        current_pos = 0
        
        # 查找所有结构标记
        section_markers = []
        for section_type, patterns in self.section_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    section_markers.append({
                        'start': match.start(),
                        'end': match.end(),
                        'type': section_type,
                        'text': match.group()
                    })
        
        # 按位置排序
        section_markers.sort(key=lambda x: x['start'])
        
        # 根据标记分割文本
        for i, marker in enumerate(section_markers):
            if i == 0 and marker['start'] > 0:
                # 添加开头部分
                partitions.append({
                    'text': text[0:marker['start']].strip(),
                    'type': 'header',
                    'start': 0,
                    'end': marker['start']
                })
            
            # 确定当前段落的结束位置
            end_pos = section_markers[i + 1]['start'] if i + 1 < len(section_markers) else len(text)
            
            partition_text = text[marker['start']:end_pos].strip()
            if len(partition_text) >= self.config.min_partition_length:
                partitions.append({
                    'text': partition_text,
                    'type': marker['type'],
                    'start': marker['start'],
                    'end': end_pos
                })
        
        return partitions
    
    def _simple_split(self, text: str, tokenizer) -> List[Dict[str, Any]]:
        """简单的固定长度分割"""
        partitions = []
        tokens = tokenizer.encode(text, add_special_tokens=False)
        
        for i in range(0, len(tokens), self.config.max_partition_length):
            end_idx = min(i + self.config.max_partition_length, len(tokens))
            partition_tokens = tokens[i:end_idx]
            partition_text = tokenizer.decode(partition_tokens, skip_special_tokens=True)
            
            partitions.append({
                'text': partition_text,
                'type': 'chunk',
                'start': i,
                'end': end_idx,
                'tokens': partition_tokens
            })
        
        return partitions


class VirtualAnnotator(nn.Module):
    """虚拟标注者网络 - 从协作标注模块迁移"""
    
    def __init__(self, config: UDPConfig, annotator_id: int, specialization: str = "general"):
        super().__init__()
        self.config = config
        self.annotator_id = annotator_id
        self.specialization = specialization
        
        # 标注者特定的网络层
        self.feature_extractor = nn.Linear(config.hidden_size, config.hidden_size // 2)
        self.classifier = nn.Linear(config.hidden_size // 2, config.hidden_size)
        self.confidence_estimator = nn.Linear(config.hidden_size // 2, 1)
        self.dropout = nn.Dropout(config.dropout)
        
    def forward(self, hidden_states: torch.Tensor) -> Dict[str, torch.Tensor]:
        """前向传播 - 修复维度和数据类型问题"""
        # 🔧 修复：确保数据类型匹配
        target_dtype = next(self.parameters()).dtype
        if hidden_states.dtype != target_dtype:
            hidden_states = hidden_states.to(target_dtype)

        # 🔧 修复：处理维度问题
        # 如果输入是3D (batch, seq_len, hidden_size)，取平均池化
        if hidden_states.dim() == 3:
            hidden_states = hidden_states.mean(dim=1)  # (batch, hidden_size)
        elif hidden_states.dim() == 2:
            # 已经是正确维度
            pass
        else:
            # 其他维度，展平到2D
            batch_size = hidden_states.size(0)
            hidden_states = hidden_states.view(batch_size, -1)

            # 如果维度不匹配，使用线性变换调整
            if hidden_states.size(-1) != self.config.hidden_size:
                if not hasattr(self, 'dimension_adapter'):
                    self.dimension_adapter = nn.Linear(hidden_states.size(-1), self.config.hidden_size).to(hidden_states.device)
                hidden_states = self.dimension_adapter(hidden_states)

        # 特征提取
        features = self.dropout(torch.relu(self.feature_extractor(hidden_states)))

        # 分类预测
        logits = self.classifier(features)

        # 置信度估计
        confidence = torch.sigmoid(self.confidence_estimator(features))

        return {
            'logits': logits,
            'confidence': confidence,
            'features': features
        }


class UnifiedDataPreprocessingModule(nn.Module):
    """
    统一数据预处理模块 (UDP)

    集成了LSE、协作标注、分区提取三个功能的统一预处理模块
    """

    def __init__(self, config: UDPConfig):
        super().__init__()
        self.config = config

        # 初始化子模块
        if config.enable_legal_semantic_enhancement:
            self.entity_recognizer = LegalEntityRecognizer(config)
            self.semantic_enhancer = self._build_semantic_enhancer()

        if config.enable_partition_extraction:
            self.text_splitter = LegalTextSplitter(config)
            self.partition_aggregator = self._build_partition_aggregator()

        if config.enable_collaborative_annotation:
            self.virtual_annotators = nn.ModuleList([
                VirtualAnnotator(config, i, spec)
                for i, spec in enumerate(["entity", "relation", "general"])
            ])
            self.annotation_fusion = self._build_annotation_fusion()

        # 特征融合层
        self.feature_fusion = self._build_feature_fusion()

        logger.info(f"UDP模块初始化完成 - LSE: {config.enable_legal_semantic_enhancement}, "
                   f"分区: {config.enable_partition_extraction}, "
                   f"协作: {config.enable_collaborative_annotation}")

    def to(self, device_or_dtype):
        """重写to方法，确保所有子模块的数据类型一致"""
        result = super().to(device_or_dtype)

        # 确保semantic_enhancer的数据类型与模型一致
        if hasattr(self, 'semantic_enhancer') and self.semantic_enhancer is not None:
            self.semantic_enhancer = self.semantic_enhancer.to(device_or_dtype)

        return result

    def _build_semantic_enhancer(self) -> nn.Module:
        """构建语义增强器"""
        return nn.Sequential(
            nn.Linear(self.config.hidden_size, self.config.hidden_size * 2),
            nn.ReLU(),
            nn.Dropout(self.config.dropout),
            nn.Linear(self.config.hidden_size * 2, self.config.hidden_size),
            nn.LayerNorm(self.config.hidden_size)
        )

    def _build_partition_aggregator(self) -> nn.Module:
        """构建分区聚合器 - 修复维度问题"""
        if self.config.aggregation_method == "attention":
            return nn.MultiheadAttention(
                embed_dim=self.config.hidden_size,
                num_heads=8,
                dropout=self.config.dropout,
                batch_first=True
            )
        else:
            # 使用平均池化而不是拼接，避免维度爆炸
            return nn.Sequential(
                nn.Linear(self.config.hidden_size, self.config.hidden_size),
                nn.ReLU(),
                nn.Dropout(self.config.dropout)
            )

    def _build_annotation_fusion(self) -> nn.Module:
        """构建标注融合层 - 修复维度问题"""
        # 使用实际的虚拟标注器数量
        actual_num_annotators = len(["entity", "relation", "general"])  # 3个
        return nn.Sequential(
            nn.Linear(self.config.hidden_size * actual_num_annotators,
                     self.config.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.config.dropout),
            nn.Linear(self.config.hidden_size, self.config.hidden_size)
        )

    def _build_feature_fusion(self) -> nn.Module:
        """构建特征融合层"""
        if self.config.feature_fusion_method == "attention":
            return nn.MultiheadAttention(
                embed_dim=self.config.hidden_size,
                num_heads=8,
                dropout=self.config.dropout,
                batch_first=True
            )
        else:
            return nn.Linear(self.config.hidden_size * 3, self.config.hidden_size)

    def forward(self,
                input_text: str,
                tokenizer,
                hidden_states: Optional[torch.Tensor] = None,
                input_ids: Optional[torch.Tensor] = None,
                attention_mask: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """
        统一数据预处理前向传播 - 优化版

        数据流：原始文本 → UDP预处理 → 增强的输入 → 大模型

        Args:
            input_text: 输入文本
            tokenizer: 分词器
            hidden_states: 可选的隐藏状态（用于后处理）
            input_ids: 可选的输入token ids
            attention_mask: 可选的注意力掩码

        Returns:
            预处理结果字典，包含增强的输入数据
        """
        device = next(self.parameters()).device

        # 验证输入
        if not self.validate_inputs(input_text, hidden_states):
            logger.warning("UDP输入验证失败，返回原始数据")
            return self._create_fallback_results(input_text, input_ids, attention_mask)

        # 初始化结果字典
        results = {
            'original_text': input_text,
            'enhanced_text': input_text,  # 增强后的文本
            'enhanced_features': {},
            'partitions': [],
            'annotations': {},
            'enhanced_input_ids': input_ids,
            'enhanced_attention_mask': attention_mask,
            'final_representation': hidden_states,
            'preprocessing_metadata': {}
        }

        try:
            # 阶段1: 文本级预处理（在tokenization之前）- LSE模块
            enhanced_text = self._stage1_text_preprocessing(input_text, device)
            results['enhanced_text'] = enhanced_text
            logger.debug("✅ UDP阶段1完成: LSE法律语义增强")

            # 阶段2: Token级预处理（处理tokenized数据）- 分区提取模块
            if tokenizer is not None:
                token_results = self._stage2_token_preprocessing(enhanced_text, tokenizer, device)
                results.update(token_results)
                logger.debug("✅ UDP阶段2完成: 分区提取 + Token增强")
            else:
                logger.debug("⚠️ UDP阶段2跳过: 无tokenizer，分区提取模块未启用")

            # 阶段3: 特征级预处理（处理hidden states）- 协作标注模块
            if hidden_states is not None:
                feature_results = self._stage3_feature_preprocessing(hidden_states, results, device)
                results.update(feature_results)
                logger.debug("✅ UDP阶段3完成: 协作标注 + Hidden States增强")
            else:
                logger.debug("⚠️ UDP阶段3跳过: 无hidden_states，协作标注模块未启用")

            # 阶段4: 最终融合和优化
            final_results = self._stage4_final_fusion(results, device)
            results.update(final_results)
            logger.debug("✅ UDP阶段4完成: 特征融合")

            # 统计启用的模块
            enabled_modules = []
            if self.config.enable_legal_semantic_enhancement:
                enabled_modules.append("LSE")
            if self.config.enable_partition_extraction and tokenizer is not None:
                enabled_modules.append("分区提取")
            if self.config.enable_collaborative_annotation and hidden_states is not None:
                enabled_modules.append("协作标注")

            logger.debug(f"✅ UDP多阶段预处理完成，启用模块: {', '.join(enabled_modules)}")

        except Exception as e:
            logger.error(f"UDP预处理失败: {e}")
            return self._create_fallback_results(input_text, input_ids, attention_mask)

        return results

    def _create_fallback_results(self, input_text: str, input_ids: Optional[torch.Tensor],
                                attention_mask: Optional[torch.Tensor]) -> Dict[str, Any]:
        """创建备用结果（当预处理失败时）"""
        return {
            'original_text': input_text,
            'enhanced_text': input_text,
            'enhanced_features': {},
            'partitions': [],
            'annotations': {},
            'enhanced_input_ids': input_ids,
            'enhanced_attention_mask': attention_mask,
            'final_representation': None,
            'preprocessing_metadata': {'status': 'fallback'}
        }

    def _stage1_text_preprocessing(self, input_text: str, device: torch.device) -> str:
        """
        阶段1: 文本级预处理
        在tokenization之前对原始文本进行增强
        """
        enhanced_text = input_text

        # 1. 法律语义增强 (LSE)
        if self.config.enable_legal_semantic_enhancement:
            enhanced_text = self._enhance_legal_semantics(enhanced_text)

        # 2. 文本清理和标准化
        enhanced_text = self._normalize_legal_text(enhanced_text)

        # 3. 关键信息标记
        enhanced_text = self._mark_key_information(enhanced_text)

        return enhanced_text

    def _stage2_token_preprocessing(self, text: str, tokenizer, device: torch.device) -> Dict[str, Any]:
        """
        阶段2: Token级预处理
        处理tokenized数据，生成增强的input_ids和attention_mask
        """
        results = {}

        # 1. 分区提取处理
        if self.config.enable_partition_extraction:
            partition_results = self._apply_partition_extraction(text, tokenizer, device)
            results['partitions'] = partition_results

            # 基于分区结果调整tokenization
            try:
                enhanced_encoding = self._adjust_tokenization_by_partitions(text, tokenizer, partition_results)
                results['enhanced_input_ids'] = enhanced_encoding.get('input_ids')
                results['enhanced_attention_mask'] = enhanced_encoding.get('attention_mask')
                logger.debug("✅ 分区提取模块：token序列增强完成")
            except Exception as e:
                logger.warning(f"分区提取token增强失败: {e}")
                # 回退到标准tokenization
                encoding = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=1024)
                results['enhanced_input_ids'] = encoding['input_ids']
                results['enhanced_attention_mask'] = encoding['attention_mask']
        else:
            # 标准tokenization
            encoding = tokenizer(text, return_tensors='pt', padding=True, truncation=True)
            results['enhanced_input_ids'] = encoding['input_ids']
            results['enhanced_attention_mask'] = encoding['attention_mask']

        return results

    def _stage3_feature_preprocessing(self, hidden_states: torch.Tensor,
                                    current_results: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
        """
        阶段3: 特征级预处理
        处理hidden states，应用协作标注等
        """
        results = {}

        # 1. 协作标注处理
        if self.config.enable_collaborative_annotation:
            try:
                annotation_results = self._apply_collaborative_annotation(hidden_states)
                results['annotations'] = annotation_results

                # 基于协作标注增强hidden states
                enhanced_hidden = self._enhance_hidden_with_annotations(hidden_states, annotation_results)
                results['final_representation'] = enhanced_hidden
                logger.debug("✅ 协作标注模块：hidden states增强完成")
            except Exception as e:
                logger.warning(f"协作标注处理失败: {e}")
                results['final_representation'] = hidden_states
        else:
            results['final_representation'] = hidden_states
            logger.debug("⚠️ 协作标注模块：未启用或无hidden_states")

        return results

    def _stage4_final_fusion(self, all_results: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
        """
        阶段4: 最终融合和优化
        将所有预处理结果融合，生成最终的增强数据
        """
        results = {}

        # 1. 特征融合 - 修复：保持原始hidden_states的形状
        original_hidden_states = all_results.get('final_representation')
        if original_hidden_states is not None:
            try:
                # 获取原始形状信息
                original_shape = original_hidden_states.shape
                logger.debug(f"原始hidden_states形状: {original_shape}")

                # 进行特征融合
                fused_features = self._fuse_all_features(all_results, device)
                logger.debug(f"融合特征形状: {fused_features.shape}")

                # 🔧 关键修复：将融合特征应用到原始hidden_states上，保持形状
                if len(original_shape) == 3:  # (batch, seq_len, hidden_size)
                    batch_size, seq_len, hidden_size = original_shape

                    if fused_features.shape == (batch_size, hidden_size):
                        # 融合特征是(batch, hidden_size)，需要扩展到(batch, seq_len, hidden_size)
                        enhanced_hidden = fused_features.unsqueeze(1).expand(batch_size, seq_len, hidden_size)
                    elif fused_features.shape == (1, hidden_size):
                        # 融合特征是(1, hidden_size)，需要扩展到原始形状
                        enhanced_hidden = fused_features.expand(batch_size, seq_len, hidden_size)
                    else:
                        # 形状不匹配，使用原始hidden_states并应用增强
                        enhancement_factor = fused_features.mean().item() if fused_features.numel() > 0 else 1.0
                        enhanced_hidden = original_hidden_states * (1.0 + enhancement_factor * 0.1)  # 轻微增强

                elif len(original_shape) == 2:  # (batch, hidden_size)
                    if fused_features.shape == original_shape:
                        enhanced_hidden = fused_features
                    else:
                        # 调整融合特征形状
                        if fused_features.numel() == original_shape.numel():
                            enhanced_hidden = fused_features.view(original_shape)
                        else:
                            enhanced_hidden = original_hidden_states
                else:
                    # 其他情况，保持原始hidden_states
                    enhanced_hidden = original_hidden_states

                # 确保数据类型一致
                if enhanced_hidden.dtype != original_hidden_states.dtype:
                    enhanced_hidden = enhanced_hidden.to(original_hidden_states.dtype)

                # 确保设备一致
                if enhanced_hidden.device != original_hidden_states.device:
                    enhanced_hidden = enhanced_hidden.to(original_hidden_states.device)

                results['final_representation'] = enhanced_hidden
                logger.debug(f"最终表示形状: {enhanced_hidden.shape}")

            except Exception as e:
                logger.error(f"特征融合失败: {e}")
                # 回退到原始hidden_states
                results['final_representation'] = original_hidden_states
        else:
            logger.warning("没有原始hidden_states，无法进行特征融合")

        # 2. 生成预处理元数据
        metadata = {
            'status': 'success',
            'stages_completed': ['text_preprocessing', 'token_preprocessing', 'feature_preprocessing', 'final_fusion'],
            'lse_applied': self.config.enable_legal_semantic_enhancement,
            'partitions_extracted': len(all_results.get('partitions', [])),
            'annotations_applied': self.config.enable_collaborative_annotation,
            'device': str(device),
            'final_shape': str(results.get('final_representation', {}).shape) if results.get('final_representation') is not None else 'None'
        }
        results['preprocessing_metadata'] = metadata

        return results

    def _enhance_legal_semantics(self, text: str) -> str:
        """增强法律语义 - 针对司法数据优化 (修复版本)"""
        try:
            enhanced_text = text

            # 🔧 修复：检查是否已经被处理过，避免重复标记
            if '[罪名]' in text or '[法院术语]' in text or '[时间]' in text:
                return text

            # 🔧 修复：严格控制文本膨胀
            original_length = len(text)

            # 1. 司法专业术语标记 (一次性处理)
            enhanced_text = self._mark_legal_terms_fixed(enhanced_text)

            # 检查膨胀率
            current_length = len(enhanced_text)
            inflation_rate = (current_length - original_length) / original_length if original_length > 0 else 0

            # 如果膨胀率已经超过15%，跳过后续处理
            if inflation_rate > 0.15:
                return enhanced_text

            # 2. 案件要素提取和标记 (一次性处理)
            enhanced_text = self._mark_case_elements_fixed(enhanced_text)

            # 最终检查
            final_length = len(enhanced_text)
            final_inflation_rate = (final_length - original_length) / original_length if original_length > 0 else 0

            if final_inflation_rate > 0.25:  # 如果最终膨胀率超过25%，回退到原文
                logger.warning(f"文本膨胀率过高 ({final_inflation_rate:.1%})，回退到原文")
                return text

            logger.debug(f"语义增强完成，膨胀率: {final_inflation_rate:.1%}")
            return enhanced_text

        except Exception as e:
            logger.error(f"法律语义增强失败: {e}")
            return text  # 出错时返回原文

    def _mark_legal_terms_fixed(self, text: str) -> str:
        """标记司法专业术语 - 修复版本，严格控制膨胀率"""
        import re

        # 🔧 严格控制：只标记最核心的术语，避免文本膨胀
        # 计算原始长度，确保膨胀率不超过20%
        original_length = len(text)
        max_allowed_length = int(original_length * 1.2)  # 最多膨胀20%

        # 只标记最重要的罪名（限制数量）
        crimes = ['故意伤害罪', '非法侵入住宅罪']  # 减少到2个最常见的
        marked_count = 0
        max_marks = 2  # 最多标记2个术语

        for crime in crimes:
            if marked_count >= max_marks:
                break
            if crime in text and f'[罪名]{crime}[/罪名]' not in text:
                new_text = text.replace(crime, f'[罪名]{crime}[/罪名]', 1)
                if len(new_text) <= max_allowed_length:
                    text = new_text
                    marked_count += 1
                else:
                    break  # 如果会超过长度限制，停止标记

        # 只标记最关键的法院术语（限制数量）
        court_terms = ['本院认为']  # 只保留最重要的一个
        for term in court_terms:
            if marked_count >= max_marks:
                break
            if term in text and f'[法院术语]{term}[/法院术语]' not in text:
                new_text = text.replace(term, f'[法院术语]{term}[/法院术语]', 1)
                if len(new_text) <= max_allowed_length:
                    text = new_text
                    marked_count += 1
                else:
                    break

        return text

    def _mark_case_elements_fixed(self, text: str) -> str:
        """标记案件要素 - 修复版本，严格控制膨胀率"""
        import re

        # 🔧 严格控制：检查当前文本长度，避免过度膨胀
        original_length = len(text)
        max_allowed_length = int(original_length * 1.15)  # 最多膨胀15%

        # 只标记最重要的时间信息，且只标记一个
        time_pattern = r'(\d{4}年(?:1[0-2]|0?[1-9])月(?:3[01]|[12][0-9]|0?[1-9])日)'
        matches = re.findall(time_pattern, text)
        if matches and '[时间]' not in text and len(text) < max_allowed_length:
            # 只标记第一个时间，且检查长度
            first_time = matches[0]
            new_text = text.replace(first_time, f'[时间]{first_time}[/时间]', 1)
            if len(new_text) <= max_allowed_length:
                text = new_text

        return text

    def _standardize_legal_articles(self, text: str) -> str:
        """标准化法条引用"""
        import re

        # 标准化法条格式
        text = re.sub(r'第(\d+)条', r'[法条]第\1条[/法条]', text)
        text = re.sub(r'《([^》]+)》第(\d+)条', r'[法律]《\1》第\2条[/法律]', text)

        return text

    def _structure_penalty_info(self, text: str) -> str:
        """结构化刑期信息"""
        import re

        # 有期徒刑
        text = re.sub(r'有期徒刑(\d+)年?(\d*)个?月?', r'[刑期]有期徒刑\1年\2个月[/刑期]', text)

        # 拘役
        text = re.sub(r'拘役(\d+)个?月', r'[刑期]拘役\1个月[/刑期]', text)

        # 管制
        text = re.sub(r'管制(\d+)个?月', r'[刑期]管制\1个月[/刑期]', text)

        # 罚金
        text = re.sub(r'罚金(\d+)元', r'[罚金]\1元[/罚金]', text)

        return text

    def _normalize_legal_text(self, text: str) -> str:
        """标准化法律文本 - 简化版本"""
        # 只做基本的文本清理，避免重复标记
        return text.strip()

    def _mark_key_information(self, text: str) -> str:
        """标记关键信息 - 已在_mark_case_elements_fixed中处理"""
        # 避免重复标记，直接返回
        return text

    def _extract_legal_entities(self, text: str) -> Dict[str, List[str]]:
        """提取法律实体"""
        entities = {
            'person': [],
            'court': [],
            'law': [],
            'crime': []
        }

        import re

        # 提取人名（简单规则）
        person_pattern = r'[被告人|原告|被害人]?([张王李赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文][某甲乙丙丁]?[某]?[一二三四五六七八九十]?)'
        persons = re.findall(person_pattern, text)
        entities['person'] = list(set(persons))

        # 提取法院名称
        court_pattern = r'([\u4e00-\u9fa5]+人民法院)'
        courts = re.findall(court_pattern, text)
        entities['court'] = list(set(courts))

        return entities

    def _adjust_tokenization_by_partitions(self, text: str, tokenizer, partitions: List[Dict]) -> Dict[str, torch.Tensor]:
        """基于分区结果调整tokenization"""
        # 如果有分区，优先处理重要分区
        if partitions:
            # 重新组织文本，突出重要分区
            important_parts = []
            for partition in partitions:
                if partition.get('importance', 0) > 0.7:  # 重要分区
                    important_parts.append(f"[重要]{partition['text']}[/重要]")
                else:
                    important_parts.append(partition['text'])

            enhanced_text = " ".join(important_parts)
        else:
            enhanced_text = text

        # 进行tokenization
        encoding = tokenizer(
            enhanced_text,
            return_tensors='pt',
            padding=True,
            truncation=True,
            max_length=1024
        )

        return {
            'input_ids': encoding['input_ids'],
            'attention_mask': encoding['attention_mask']
        }

    def _enhance_hidden_with_annotations(self, hidden_states: torch.Tensor,
                                       annotations: Dict[str, Any]) -> torch.Tensor:
        """基于协作标注增强hidden states - 修复版本"""
        if not annotations:
            logger.debug("没有协作标注，返回原始hidden_states")
            return hidden_states

        try:
            original_shape = hidden_states.shape
            logger.debug(f"增强前hidden_states形状: {original_shape}")

            enhanced_hidden = hidden_states.clone()  # 创建副本避免修改原始数据

            # 方法1: 使用attention_weights（如果存在）
            if 'attention_weights' in annotations:
                attention_weights = annotations['attention_weights']
                logger.debug(f"attention_weights形状: {attention_weights.shape}")

                if attention_weights.shape == hidden_states.shape[:2]:  # [batch, seq_len]
                    # 广播attention weights到hidden dimension
                    attention_weights = attention_weights.unsqueeze(-1)  # [batch, seq_len, 1]
                    enhanced_hidden = hidden_states * attention_weights
                    logger.debug("使用attention_weights增强hidden_states")
                else:
                    logger.warning(f"attention_weights形状不匹配: {attention_weights.shape} vs {hidden_states.shape[:2]}")

            # 方法2: 使用consensus和confidence_scores
            elif 'consensus' in annotations and annotations['consensus'] is not None:
                consensus = annotations['consensus']
                confidence_scores = annotations.get('confidence_scores')

                logger.debug(f"consensus形状: {consensus.shape}")
                if confidence_scores is not None:
                    logger.debug(f"confidence_scores形状: {confidence_scores.shape}")

                # 根据consensus调整hidden_states
                if len(original_shape) == 3:  # (batch, seq_len, hidden_size)
                    batch_size, seq_len, hidden_size = original_shape

                    if consensus.shape == (batch_size, hidden_size):
                        # consensus是(batch, hidden_size)，需要广播到每个时间步
                        consensus_expanded = consensus.unsqueeze(1).expand(batch_size, seq_len, hidden_size)

                        # 使用confidence作为混合权重
                        if confidence_scores is not None and confidence_scores.shape == (batch_size, 1):
                            mix_weight = confidence_scores.unsqueeze(1).expand(batch_size, seq_len, 1) * 0.1  # 轻微混合
                            enhanced_hidden = hidden_states * (1 - mix_weight) + consensus_expanded * mix_weight
                        else:
                            # 没有confidence，使用固定权重
                            enhanced_hidden = hidden_states * 0.9 + consensus_expanded * 0.1

                        logger.debug("使用consensus增强hidden_states")

                    elif consensus.shape == original_shape:
                        # consensus形状完全匹配
                        if confidence_scores is not None:
                            # 使用confidence作为混合权重
                            avg_confidence = confidence_scores.mean().item()
                            mix_weight = min(avg_confidence * 0.2, 0.3)  # 最多30%的混合
                            enhanced_hidden = hidden_states * (1 - mix_weight) + consensus * mix_weight
                        else:
                            enhanced_hidden = hidden_states * 0.8 + consensus * 0.2

                        logger.debug("使用形状匹配的consensus增强hidden_states")

                    else:
                        logger.warning(f"consensus形状不匹配，无法直接应用: {consensus.shape} vs {original_shape}")
                        # 尝试通过平均池化或其他方式调整
                        if consensus.numel() == hidden_size:
                            # consensus可能是一维的，扩展到正确形状
                            consensus_reshaped = consensus.view(1, hidden_size).expand(batch_size, seq_len, hidden_size)
                            enhanced_hidden = hidden_states * 0.9 + consensus_reshaped * 0.1
                            logger.debug("通过reshape应用consensus")

                elif len(original_shape) == 2:  # (batch, hidden_size)
                    if consensus.shape == original_shape:
                        enhanced_hidden = hidden_states * 0.8 + consensus * 0.2
                        logger.debug("2D情况下使用consensus增强")

            # 确保输出形状与输入一致
            if enhanced_hidden.shape != original_shape:
                logger.error(f"增强后形状不匹配: {enhanced_hidden.shape} vs {original_shape}")
                enhanced_hidden = hidden_states  # 回退到原始数据

            logger.debug(f"增强后hidden_states形状: {enhanced_hidden.shape}")
            return enhanced_hidden

        except Exception as e:
            logger.error(f"协作标注增强失败: {e}")
            return hidden_states  # 出错时返回原始数据

    def preprocess_for_model(self, batch_data: Dict[str, Any], tokenizer) -> Dict[str, Any]:
        """
        为模型预处理批量数据 - 主要接口

        这是UDP模块的主要对外接口，用于处理训练/推理数据

        Args:
            batch_data: 批量数据，包含texts, input_ids, attention_mask等
            tokenizer: 分词器

        Returns:
            预处理后的批量数据，可直接传入模型
        """
        device = next(self.parameters()).device
        processed_batch = {}

        # 处理文本数据
        if 'texts' in batch_data and batch_data['texts']:
            texts = batch_data['texts']
            if isinstance(texts, str):
                texts = [texts]

            enhanced_texts = []
            all_partitions = []

            # 对每个文本进行预处理
            for text in texts:
                # 阶段1: 文本增强
                enhanced_text = self._stage1_text_preprocessing(text, device)
                enhanced_texts.append(enhanced_text)

                # 阶段2: 分区提取
                if self.config.enable_partition_extraction:
                    partitions = self._apply_partition_extraction(enhanced_text, tokenizer, device)
                    all_partitions.append(partitions)

            # 重新tokenize增强后的文本
            if enhanced_texts:
                enhanced_encoding = tokenizer(
                    enhanced_texts,
                    return_tensors='pt',
                    padding=True,
                    truncation=True,
                    max_length=1024
                ).to(device)

                processed_batch['input_ids'] = enhanced_encoding['input_ids']
                processed_batch['attention_mask'] = enhanced_encoding['attention_mask']
                processed_batch['texts'] = enhanced_texts
                processed_batch['partitions'] = all_partitions
            else:
                # 保持原始数据
                processed_batch.update(batch_data)
        else:
            # 保持原始数据
            processed_batch.update(batch_data)

        # 移动其他数据到正确设备
        for key, value in batch_data.items():
            if key not in processed_batch and hasattr(value, 'to'):
                processed_batch[key] = value.to(device)
            elif key not in processed_batch:
                processed_batch[key] = value

        return processed_batch

    def enhance_with_legal_semantics(self, text: str, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        使用法律语义增强hidden states - 兼容接口

        这个方法被创新管理器调用，用于增强模型的hidden states
        """
        if hidden_states is None:
            return None

        device = hidden_states.device

        # 应用协作标注
        if self.config.enable_collaborative_annotation:
            annotation_results = self._apply_collaborative_annotation(hidden_states)
            enhanced_hidden = self._enhance_hidden_with_annotations(hidden_states, annotation_results)
            return enhanced_hidden

        return hidden_states

    def _apply_legal_semantic_enhancement(self, text: str, device: torch.device) -> Dict[str, Any]:
        """应用法律语义增强"""
        lse_results = {}

        # 实体识别
        if self.config.entity_recognition_enabled:
            entities = self.entity_recognizer.extract_entities(text)
            lse_results['entities'] = entities

        # 语义增强
        if hasattr(self, 'semantic_enhancer'):
            # 创建文本的基础表示
            text_tensor = torch.randn(1, len(text.split()), self.config.hidden_size, device=device)
            enhanced = self.semantic_enhancer(text_tensor)
            lse_results['semantic_enhancement'] = enhanced

        return lse_results

    def _apply_partition_extraction(self, text: str, tokenizer, device: torch.device) -> List[Dict[str, Any]]:
        """应用分区提取处理 - 针对司法文书结构优化，修复版本"""
        partitions = []

        try:
            # 🔧 修复：更鲁棒的分区提取
            logger.debug(f"开始分区提取，文本长度: {len(text)}")

            # 司法文书结构化分区 - 适配数据加载器格式
            fact_sections = self._extract_fact_sections(text)
            court_sections = self._extract_court_view_sections(text)
            defendant_sections = self._extract_defendant_sections(text)
            judgment_sections = self._extract_judgment_sections(text)
            legal_sections = self._extract_legal_basis_sections(text)

            partitions.extend(fact_sections)
            partitions.extend(court_sections)
            partitions.extend(defendant_sections)
            partitions.extend(judgment_sections)
            partitions.extend(legal_sections)

            logger.debug(f"结构化分区提取结果: {len(partitions)} 个分区")

            # 如果结构化分区失败，使用简单分区
            if not partitions:
                logger.debug("结构化分区为空，使用简单分区")
                partitions = self._simple_sentence_partition(text, tokenizer, device)

            # 为每个分区添加特征表示
            for i, partition in enumerate(partitions):
                try:
                    if 'tokens' not in partition:
                        if hasattr(tokenizer, 'encode'):
                            tokens = tokenizer.encode(partition['text'], add_special_tokens=False)
                        else:
                            # 回退方案：简单字符编码
                            tokens = [ord(c) % 1000 for c in partition['text'][:50]]
                        partition['tokens'] = tokens

                    # 创建分区的向量表示
                    partition_length = min(len(partition['tokens']), self.config.max_partition_length)
                    if partition_length > 0:
                        partition_tensor = torch.randn(1, partition_length, self.config.hidden_size, device=device)
                        partition['representation'] = partition_tensor
                    else:
                        # 空分区的默认表示
                        partition['representation'] = torch.zeros(1, 1, self.config.hidden_size, device=device)

                except Exception as e:
                    logger.warning(f"分区 {i} 特征表示创建失败: {e}")
                    # 创建默认表示
                    partition['representation'] = torch.zeros(1, 1, self.config.hidden_size, device=device)

        except Exception as e:
            logger.warning(f"司法分区提取失败: {e}")
            # 回退到简单分区
            try:
                partitions = self._simple_sentence_partition(text, tokenizer, device)
            except Exception as e2:
                logger.error(f"简单分区也失败: {e2}")
                # 最终回退：创建单个分区
                partitions = [{
                    'id': 'fallback_0',
                    'text': text[:100] + '...' if len(text) > 100 else text,
                    'type': 'fallback',
                    'importance': 0.5,
                    'start_pos': 0,
                    'end_pos': len(text),
                    'representation': torch.zeros(1, 1, self.config.hidden_size, device=device)
                }]

        logger.debug(f"最终分区数量: {len(partitions)}")
        return partitions

    def _extract_fact_sections(self, text: str) -> List[Dict[str, Any]]:
        """提取案件事实部分 - 修复版本"""
        import re
        partitions = []

        # 方法1: 按行分割查找（最可靠）
        lines = text.split('\n')
        for line in lines:
            if line.startswith('案件事实：'):
                content = line[4:].strip()  # 去掉"案件事实："
                if content:
                    partitions.append({
                        'id': 'fact_section_0',
                        'text': f"案件事实：{content}",
                        'type': 'case_fact',
                        'importance': 0.9,
                        'start_pos': text.find(line),
                        'end_pos': text.find(line) + len(line)
                    })
                    break

        # 方法2: 如果方法1失败，使用简化正则
        if not partitions:
            section_pattern = r'案件事实：(.*?)(?=\n法院观点：|\n被告信息：|\n判决结果：|$)'
            section_matches = re.findall(section_pattern, text, re.DOTALL)

            for i, section_text in enumerate(section_matches):
                if section_text.strip():
                    partitions.append({
                        'id': f'fact_section_{i}',
                        'text': f"案件事实：{section_text.strip()}",
                        'type': 'case_fact',
                        'importance': 0.9,
                        'start_pos': text.find(f"案件事实：{section_text}"),
                        'end_pos': text.find(f"案件事实：{section_text}") + len(f"案件事实：{section_text}")
                    })

        # 方法3: 如果仍然没有找到，回退到原始模式（兼容性）
        if not partitions:
            fact_patterns = [
                r'(公诉机关指控[^。]*?。)',
                r'(经审理查明[^。]*?。)',
                r'(\d{4}年\d{1,2}月\d{1,2}日[^。]*?。)',
                r'(被告人[^。]*?的行为[^。]*?。)'
            ]

            for i, pattern in enumerate(fact_patterns):
                matches = re.findall(pattern, text)
                for j, match in enumerate(matches):
                    partitions.append({
                        'id': f'fact_{i}_{j}',
                        'text': match,
                        'type': 'case_fact',
                        'importance': 0.9,
                        'start_pos': text.find(match),
                        'end_pos': text.find(match) + len(match)
                    })

        return partitions

    def _extract_court_view_sections(self, text: str) -> List[Dict[str, Any]]:
        """提取法院观点部分 - 修复版本"""
        import re
        partitions = []

        # 方法1: 按行分割查找（最可靠）
        lines = text.split('\n')
        for line in lines:
            if line.startswith('法院观点：'):
                content = line[4:].strip()  # 去掉"法院观点："
                if content:
                    partitions.append({
                        'id': 'court_view_section_0',
                        'text': f"法院观点：{content}",
                        'type': 'court_opinion',
                        'importance': 0.8,
                        'start_pos': text.find(line),
                        'end_pos': text.find(line) + len(line)
                    })
                    break

        # 方法2: 如果方法1失败，使用简化正则
        if not partitions:
            section_pattern = r'法院观点：(.*?)(?=\n案件事实：|\n被告信息：|\n判决结果：|$)'
            section_matches = re.findall(section_pattern, text, re.DOTALL)

            for i, section_text in enumerate(section_matches):
                if section_text.strip():
                    partitions.append({
                        'id': f'court_view_section_{i}',
                        'text': f"法院观点：{section_text.strip()}",
                        'type': 'court_opinion',
                        'importance': 0.8,
                        'start_pos': text.find(f"法院观点：{section_text}"),
                        'end_pos': text.find(f"法院观点：{section_text}") + len(f"法院观点：{section_text}")
                    })

        # 方法3: 如果仍然没有找到，回退到原始模式（兼容性）
        if not partitions:
            court_patterns = [
                r'(本院认为[^。]*?。)',
                r'(依照[^。]*?法[^。]*?条[^。]*?。)',
                r'(根据[^。]*?法[^。]*?条[^。]*?。)'
            ]

            for i, pattern in enumerate(court_patterns):
                matches = re.findall(pattern, text)
                for j, match in enumerate(matches):
                    partitions.append({
                        'id': f'court_view_{i}_{j}',
                        'text': match,
                        'type': 'court_opinion',
                        'importance': 0.8,
                        'start_pos': text.find(match),
                        'end_pos': text.find(match) + len(match)
                    })

        return partitions

    def _extract_judgment_sections(self, text: str) -> List[Dict[str, Any]]:
        """提取判决部分 - 修复版本"""
        import re
        partitions = []

        # 方法1: 按行分割查找（最可靠）
        lines = text.split('\n')
        for line in lines:
            if line.startswith('判决结果：'):
                content = line[4:].strip()  # 去掉"判决结果："
                if content:
                    partitions.append({
                        'id': 'judgment_section_0',
                        'text': f"判决结果：{content}",
                        'type': 'judgment_result',
                        'importance': 0.95,
                        'start_pos': text.find(line),
                        'end_pos': text.find(line) + len(line)
                    })
                    break

        # 方法2: 如果方法1失败，使用简化正则
        if not partitions:
            section_pattern = r'判决结果：(.*?)(?=\n案件事实：|\n法院观点：|\n被告信息：|$)'
            section_matches = re.findall(section_pattern, text, re.DOTALL)

            for i, section_text in enumerate(section_matches):
                if section_text.strip():
                    partitions.append({
                        'id': f'judgment_section_{i}',
                        'text': f"判决结果：{section_text.strip()}",
                        'type': 'judgment_result',
                        'importance': 0.95,
                        'start_pos': text.find(f"判决结果：{section_text}"),
                        'end_pos': text.find(f"判决结果：{section_text}") + len(f"判决结果：{section_text}")
                    })

        # 方法3: 如果仍然没有找到，回退到原始模式（兼容性）
        if not partitions:
            judgment_patterns = [
                r'(判处[^。]*?。)',
                r'(有期徒刑[^。]*?。)',
                r'(拘役[^。]*?。)',
                r'(罚金[^。]*?。)'
            ]

            for i, pattern in enumerate(judgment_patterns):
                matches = re.findall(pattern, text)
                for j, match in enumerate(matches):
                    partitions.append({
                        'id': f'judgment_{i}_{j}',
                        'text': match,
                        'type': 'judgment_result',
                        'importance': 0.95,
                        'start_pos': text.find(match),
                        'end_pos': text.find(match) + len(match)
                    })

        return partitions

    def _extract_defendant_sections(self, text: str) -> List[Dict[str, Any]]:
        """提取被告信息部分 - 修复版本"""
        import re
        partitions = []

        # 方法1: 按行分割查找（最可靠）
        lines = text.split('\n')
        for line in lines:
            if line.startswith('被告信息：'):
                content = line[4:].strip()  # 去掉"被告信息："
                if content:
                    partitions.append({
                        'id': 'defendant_section_0',
                        'text': f"被告信息：{content}",
                        'type': 'defendant_info',
                        'importance': 0.85,
                        'start_pos': text.find(line),
                        'end_pos': text.find(line) + len(line)
                    })
                    break

        # 方法2: 如果方法1失败，使用简化正则
        if not partitions:
            section_pattern = r'被告信息：(.*?)(?=\n案件事实：|\n法院观点：|\n判决结果：|$)'
            section_matches = re.findall(section_pattern, text, re.DOTALL)

            for i, section_text in enumerate(section_matches):
                if section_text.strip():
                    partitions.append({
                        'id': f'defendant_section_{i}',
                        'text': f"被告信息：{section_text.strip()}",
                        'type': 'defendant_info',
                        'importance': 0.85,
                        'start_pos': text.find(f"被告信息：{section_text}"),
                        'end_pos': text.find(f"被告信息：{section_text}") + len(f"被告信息：{section_text}")
                    })

        return partitions

    def _extract_legal_basis_sections(self, text: str) -> List[Dict[str, Any]]:
        """提取法律依据部分"""
        import re
        partitions = []

        # 查找法律依据
        legal_patterns = [
            r'(《[^》]*》第\d+条[^。]*?。)',
            r'(第\d+条[^。]*?。)'
        ]

        for i, pattern in enumerate(legal_patterns):
            matches = re.findall(pattern, text)
            for j, match in enumerate(matches):
                partitions.append({
                    'id': f'legal_basis_{i}_{j}',
                    'text': match,
                    'type': 'legal_article',
                    'importance': 0.7,  # 法律依据重要
                    'start_pos': text.find(match),
                    'end_pos': text.find(match) + len(match)
                })

        return partitions

    def _simple_sentence_partition(self, text: str, tokenizer, device: torch.device) -> List[Dict[str, Any]]:
        """简单句子分区 - 回退方案，修复版本"""
        partitions = []

        try:
            sentences = text.split('。')

            for i, sentence in enumerate(sentences):
                sentence = sentence.strip()
                if len(sentence) > 10:
                    partition = {
                        'id': f'sentence_{i}',
                        'text': sentence + '。',
                        'type': 'sentence',
                        'importance': 0.5,
                        'start_pos': text.find(sentence),
                        'end_pos': text.find(sentence) + len(sentence)
                    }

                    # 🔧 修复：安全的token处理
                    try:
                        if hasattr(tokenizer, 'encode'):
                            tokens = tokenizer.encode(partition['text'], add_special_tokens=False)
                        else:
                            # 回退方案：简单字符编码
                            tokens = [ord(c) % 1000 for c in partition['text'][:50]]

                        partition['tokens'] = tokens
                        partition_length = min(len(tokens), self.config.max_partition_length)

                        if partition_length > 0:
                            partition_tensor = torch.randn(1, partition_length, self.config.hidden_size, device=device)
                        else:
                            partition_tensor = torch.zeros(1, 1, self.config.hidden_size, device=device)

                        partition['representation'] = partition_tensor
                        partitions.append(partition)

                    except Exception as e:
                        logger.warning(f"句子分区 {i} 处理失败: {e}")
                        # 创建最小分区
                        partition['tokens'] = [1, 2, 3]  # 默认tokens
                        partition['representation'] = torch.zeros(1, 1, self.config.hidden_size, device=device)
                        partitions.append(partition)

        except Exception as e:
            logger.error(f"简单分区失败: {e}")
            # 最终回退：创建单个分区
            partitions = [{
                'id': 'simple_fallback_0',
                'text': text[:50] + '...' if len(text) > 50 else text,
                'type': 'simple_fallback',
                'importance': 0.5,
                'start_pos': 0,
                'end_pos': len(text),
                'tokens': [1, 2, 3],
                'representation': torch.zeros(1, 1, self.config.hidden_size, device=device)
            }]

        return partitions

    def _apply_collaborative_annotation(self, hidden_states: torch.Tensor) -> Dict[str, Any]:
        """应用协作标注处理 - 修复张量形状不匹配问题"""
        annotation_results = {
            'annotator_outputs': [],
            'consensus': None,
            'confidence_scores': []
        }

        # 每个虚拟标注者的输出
        annotator_outputs = []
        confidences = []

        try:
            for i, annotator in enumerate(self.virtual_annotators):
                try:
                    output = annotator(hidden_states)
                    logger.debug(f"标注者 {i} 输出形状 - logits: {output['logits'].shape}, confidence: {output['confidence'].shape}")
                    annotator_outputs.append(output['logits'])
                    confidences.append(output['confidence'])
                    annotation_results['annotator_outputs'].append(output)
                except Exception as e:
                    logger.error(f"标注者 {i} 处理失败: {e}")
                    # 创建默认输出
                    default_logits = torch.zeros(hidden_states.size(0), self.config.hidden_size, device=hidden_states.device)
                    default_confidence = torch.tensor([[0.5]], device=hidden_states.device)
                    annotator_outputs.append(default_logits)
                    confidences.append(default_confidence)
                    annotation_results['annotator_outputs'].append({
                        'logits': default_logits,
                        'confidence': default_confidence,
                        'features': default_logits
                    })

            # 计算一致性和融合结果
            if annotator_outputs:
                # 🔧 修复：确保所有logits张量形状一致
                if annotator_outputs:
                    # 获取第一个输出作为参考
                    reference_logits = annotator_outputs[0]
                    target_shape = reference_logits.shape
                    logger.debug(f"目标logits形状: {target_shape}")

                    aligned_logits = []
                    for i, logits in enumerate(annotator_outputs):
                        logger.debug(f"标注者 {i} logits形状: {logits.shape}")

                        if logits.shape == target_shape:
                            aligned_logits.append(logits)
                        else:
                            # 调整形状以匹配目标形状
                            try:
                                if logits.numel() == target_shape.numel():
                                    # 元素数量相同，直接reshape
                                    aligned_logits.append(logits.view(target_shape))
                                else:
                                    # 元素数量不同，创建目标形状的张量
                                    aligned_tensor = torch.zeros(target_shape, device=logits.device, dtype=logits.dtype)
                                    # 复制尽可能多的数据
                                    min_elements = min(logits.numel(), aligned_tensor.numel())
                                    aligned_tensor.view(-1)[:min_elements] = logits.view(-1)[:min_elements]
                                    aligned_logits.append(aligned_tensor)
                            except Exception as e:
                                logger.warning(f"标注者 {i} logits形状调整失败: {e}")
                                # 创建默认张量
                                aligned_logits.append(torch.zeros(target_shape, device=logits.device, dtype=logits.dtype))

                    # 简单的平均融合
                    if aligned_logits and len(aligned_logits) > 0:
                        try:
                            consensus = torch.stack(aligned_logits).mean(dim=0)
                            logger.debug(f"融合后consensus形状: {consensus.shape}")
                        except Exception as e:
                            logger.error(f"logits堆叠失败: {e}")
                            consensus = aligned_logits[0]  # 回退到第一个输出
                    else:
                        consensus = reference_logits  # 回退到参考输出
                else:
                    # 没有输出，创建默认consensus
                    consensus = torch.zeros(hidden_states.size(0), self.config.hidden_size, device=hidden_states.device)

                # 🔧 修复：确保confidence张量形状一致
                if confidences:
                    logger.debug(f"处理 {len(confidences)} 个confidence张量")

                    # 获取目标batch size
                    target_batch_size = hidden_states.size(0)
                    aligned_confidences = []

                    for i, conf in enumerate(confidences):
                        logger.debug(f"标注者 {i} confidence形状: {conf.shape}")

                        try:
                            # 统一调整为[batch, 1]形状
                            if conf.dim() == 0:
                                # 标量，扩展为[batch, 1]
                                aligned_conf = conf.unsqueeze(0).unsqueeze(0).expand(target_batch_size, 1)
                            elif conf.dim() == 1:
                                if conf.size(0) == target_batch_size:
                                    aligned_conf = conf.unsqueeze(-1)
                                else:
                                    # 调整batch维度
                                    aligned_conf = torch.full((target_batch_size, 1), conf.mean().item(),
                                                            device=conf.device, dtype=conf.dtype)
                            elif conf.dim() == 2:
                                if conf.size(0) == target_batch_size:
                                    if conf.size(-1) == 1:
                                        aligned_conf = conf
                                    else:
                                        # 如果最后一维不是1，取平均
                                        aligned_conf = conf.mean(dim=-1, keepdim=True)
                                else:
                                    # batch维度不匹配，创建新张量
                                    aligned_conf = torch.full((target_batch_size, 1), conf.mean().item(),
                                                            device=conf.device, dtype=conf.dtype)
                            else:
                                # 其他维度，创建默认张量
                                aligned_conf = torch.full((target_batch_size, 1), 0.5,
                                                         device=conf.device, dtype=conf.dtype)

                            aligned_confidences.append(aligned_conf)
                            logger.debug(f"标注者 {i} 对齐后confidence形状: {aligned_conf.shape}")

                        except Exception as e:
                            logger.warning(f"标注者 {i} confidence处理失败: {e}")
                            # 创建默认confidence
                            default_conf = torch.full((target_batch_size, 1), 0.5,
                                                     device=hidden_states.device, dtype=hidden_states.dtype)
                            aligned_confidences.append(default_conf)

                    # 计算平均confidence
                    if aligned_confidences:
                        try:
                            avg_confidence = torch.stack(aligned_confidences).mean(dim=0)
                            logger.debug(f"平均confidence形状: {avg_confidence.shape}")
                        except Exception as e:
                            logger.error(f"confidence堆叠失败: {e}")
                            avg_confidence = aligned_confidences[0]  # 回退到第一个
                    else:
                        avg_confidence = torch.full((target_batch_size, 1), 0.5, device=hidden_states.device)
                else:
                    # 没有confidence，创建默认值
                    target_batch_size = hidden_states.size(0)
                    avg_confidence = torch.full((target_batch_size, 1), 0.5, device=hidden_states.device)

                annotation_results['consensus'] = consensus
                annotation_results['confidence_scores'] = avg_confidence

        except Exception as e:
            logger.warning(f"协作标注处理失败: {e}")
            # 返回默认结果
            annotation_results['consensus'] = hidden_states
            annotation_results['confidence_scores'] = torch.tensor([[0.5]], device=hidden_states.device)

        return annotation_results

    def _fuse_all_features(self, results: Dict[str, Any], device: torch.device) -> torch.Tensor:
        """融合所有特征"""
        features_to_fuse = []

        # LSE特征
        if 'semantic_enhancement' in results['enhanced_features']:
            lse_features = results['enhanced_features']['semantic_enhancement']
            features_to_fuse.append(lse_features.mean(dim=1))  # 池化到固定维度

        # 分区特征
        if results['partitions']:
            partition_features = []
            for partition in results['partitions']:
                if 'representation' in partition:
                    try:
                        # 确保representation是正确的张量
                        repr_tensor = partition['representation']
                        if repr_tensor.dim() == 3:
                            # (1, seq_len, hidden_size) -> (1, hidden_size)
                            pooled = repr_tensor.mean(dim=1)
                        elif repr_tensor.dim() == 2:
                            # (seq_len, hidden_size) -> (1, hidden_size)
                            pooled = repr_tensor.mean(dim=0, keepdim=True)
                        else:
                            # 其他情况，创建默认张量
                            pooled = torch.zeros(1, self.config.hidden_size, device=device)

                        partition_features.append(pooled)
                        logger.debug(f"分区特征形状: {pooled.shape}")
                    except Exception as e:
                        logger.warning(f"分区特征处理失败: {e}")
                        # 创建默认特征
                        default_feature = torch.zeros(1, self.config.hidden_size, device=device)
                        partition_features.append(default_feature)

            if partition_features:
                try:
                    # 🔧 修复：确保所有分区特征形状一致
                    aligned_partition_features = []
                    target_shape = partition_features[0].shape

                    for i, feat in enumerate(partition_features):
                        if feat.shape == target_shape:
                            aligned_partition_features.append(feat)
                        else:
                            logger.warning(f"分区 {i} 特征形状不匹配: {feat.shape} vs {target_shape}")
                            # 调整形状
                            if feat.numel() == target_shape.numel():
                                aligned_partition_features.append(feat.view(target_shape))
                            else:
                                # 创建目标形状的默认张量
                                default_feat = torch.zeros(target_shape, device=feat.device, dtype=feat.dtype)
                                aligned_partition_features.append(default_feat)

                    # 聚合分区特征
                    if len(aligned_partition_features) > 1:
                        stacked_partitions = torch.stack(aligned_partition_features)
                        logger.debug(f"堆叠后分区特征形状: {stacked_partitions.shape}")

                        if self.config.aggregation_method == "attention":
                            aggregated, _ = self.partition_aggregator(
                                stacked_partitions, stacked_partitions, stacked_partitions
                            )
                            features_to_fuse.append(aggregated.mean(dim=0))
                        else:
                            # 使用平均池化
                            averaged = stacked_partitions.mean(dim=0)
                            aggregated = self.partition_aggregator(averaged)
                            features_to_fuse.append(aggregated)
                    else:
                        # 只有一个分区特征
                        single_feature = aligned_partition_features[0]
                        if hasattr(self, 'partition_aggregator'):
                            aggregated = self.partition_aggregator(single_feature)
                            features_to_fuse.append(aggregated)
                        else:
                            features_to_fuse.append(single_feature)

                except Exception as e:
                    logger.error(f"分区特征聚合失败: {e}")
                    # 创建默认特征
                    default_partition_feature = torch.zeros(1, self.config.hidden_size, device=device)
                    features_to_fuse.append(default_partition_feature)

        # 协作标注特征
        if 'consensus' in results['annotations'] and results['annotations']['consensus'] is not None:
            annotation_features = results['annotations']['consensus']
            features_to_fuse.append(annotation_features.mean(dim=1))

        # 最终融合
        if features_to_fuse:
            try:
                if len(features_to_fuse) == 1:
                    final_features = features_to_fuse[0]
                    logger.debug(f"单一特征，形状: {final_features.shape}")
                else:
                    # 🔧 修复：确保所有特征维度一致
                    aligned_features = []
                    target_shape = None

                    # 找到一个有效的目标形状
                    for feat in features_to_fuse:
                        if feat is not None and feat.numel() > 0:
                            if feat.dim() == 2:
                                target_shape = (1, feat.size(-1))
                            elif feat.dim() == 1:
                                target_shape = (1, feat.size(0))
                            else:
                                target_shape = (1, self.config.hidden_size)
                            break

                    if target_shape is None:
                        target_shape = (1, self.config.hidden_size)

                    logger.debug(f"目标特征形状: {target_shape}")

                    for i, feat in enumerate(features_to_fuse):
                        try:
                            if feat.dim() == 3:
                                # (batch, seq, hidden) -> (1, hidden)
                                aligned_feat = feat.mean(dim=1).mean(dim=0, keepdim=True)
                            elif feat.dim() == 2:
                                if feat.size(0) == 1:
                                    aligned_feat = feat
                                else:
                                    # (seq, hidden) -> (1, hidden)
                                    aligned_feat = feat.mean(dim=0, keepdim=True)
                            elif feat.dim() == 1:
                                aligned_feat = feat.unsqueeze(0)
                            else:
                                # 创建默认特征
                                aligned_feat = torch.zeros(target_shape, device=feat.device, dtype=feat.dtype)

                            # 确保最终形状匹配
                            if aligned_feat.shape != target_shape:
                                if aligned_feat.numel() == target_shape[1]:
                                    aligned_feat = aligned_feat.view(target_shape)
                                else:
                                    aligned_feat = torch.zeros(target_shape, device=feat.device, dtype=feat.dtype)

                            aligned_features.append(aligned_feat)
                            logger.debug(f"特征 {i} 对齐后形状: {aligned_feat.shape}")

                        except Exception as e:
                            logger.warning(f"特征 {i} 对齐失败: {e}")
                            # 创建默认特征
                            default_feat = torch.zeros(target_shape, device=device)
                            aligned_features.append(default_feat)

                    # 特征融合
                    if len(aligned_features) > 1:
                        if self.config.feature_fusion_method == "attention":
                            try:
                                stacked_features = torch.stack(aligned_features, dim=1)  # (1, num_features, hidden)
                                fused, _ = self.feature_fusion(
                                    stacked_features, stacked_features, stacked_features
                                )
                                final_features = fused.mean(dim=1)  # (1, hidden)
                            except Exception as e:
                                logger.error(f"注意力融合失败: {e}")
                                # 回退到平均融合
                                final_features = torch.stack(aligned_features).mean(dim=0)
                        else:
                            # 简单平均融合
                            final_features = torch.stack(aligned_features).mean(dim=0)
                    else:
                        final_features = aligned_features[0]

                    logger.debug(f"最终特征形状: {final_features.shape}")

            except Exception as e:
                logger.error(f"特征融合失败: {e}")
                # 创建默认特征
                final_features = torch.zeros(1, self.config.hidden_size, device=device)
        else:
            # 如果没有特征，返回零向量
            final_features = torch.zeros(1, self.config.hidden_size, device=device)
            logger.debug("没有特征需要融合，使用默认零向量")

        return final_features

    def extract_legal_features(self, text: str) -> Dict[str, Any]:
        """提取法律特征 - 兼容性方法"""
        if self.config.enable_legal_semantic_enhancement:
            return self.entity_recognizer.extract_entities(text)
        return {}

    def enhance_with_legal_semantics(self, text: str, hidden_states: torch.Tensor) -> torch.Tensor:
        """法律语义增强 - 兼容性方法 - 修复数据类型不匹配"""
        if hasattr(self, 'semantic_enhancer') and self.semantic_enhancer is not None:
            # 🔧 关键修复：确保数据类型匹配
            target_dtype = next(self.semantic_enhancer.parameters()).dtype
            if hidden_states.dtype != target_dtype:
                hidden_states = hidden_states.to(target_dtype)

            return self.semantic_enhancer(hidden_states)
        return hidden_states

    def apply_collaborative_annotation(self, hidden_states: torch.Tensor) -> Dict[str, Any]:
        """协作标注 - 兼容性方法"""
        if self.config.enable_collaborative_annotation:
            return self._apply_collaborative_annotation(hidden_states)
        return {}

    def apply_partition_extraction(self, text: str, tokenizer) -> List[Dict[str, Any]]:
        """分区提取 - 兼容性方法"""
        if self.config.enable_partition_extraction:
            device = next(self.parameters()).device
            return self._apply_partition_extraction(text, tokenizer, device)
        return []

    def get_config_summary(self) -> Dict[str, Any]:
        """获取UDP模块配置摘要"""
        return {
            'module_name': 'UDP (Unified Data Preprocessing)',
            'components': {
                'lse_enabled': self.config.enable_legal_semantic_enhancement,
                'collaborative_enabled': self.config.enable_collaborative_annotation,
                'partition_enabled': self.config.enable_partition_extraction
            },
            'parameters': {
                'hidden_size': self.config.hidden_size,
                'num_annotators': self.config.num_annotators,
                'num_partitions': self.config.num_partitions,
                'feature_fusion_method': self.config.feature_fusion_method
            }
        }

    def validate_inputs(self, text: str, hidden_states: Optional[torch.Tensor] = None) -> bool:
        """验证输入参数"""
        if not isinstance(text, str):
            logger.error("输入文本必须是字符串")
            return False

        if len(text.strip()) == 0:
            logger.warning("输入文本为空")
            return False

        if hidden_states is not None:
            if not isinstance(hidden_states, torch.Tensor):
                logger.error("hidden_states必须是torch.Tensor")
                return False

            if hidden_states.dim() != 3:
                logger.error(f"hidden_states维度错误，期望3维，实际{hidden_states.dim()}维")
                return False

        return True
